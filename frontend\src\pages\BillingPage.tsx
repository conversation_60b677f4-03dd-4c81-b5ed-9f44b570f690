import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import {
  CreditCardIcon,
  CheckCircleIcon,
  XMarkIcon,
  ArrowUpIcon,
  DocumentTextIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  StarIcon,
  BoltIcon,
  RocketLaunchIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline'
import { CheckIcon } from '@heroicons/react/24/solid'
import { billingApi } from '../lib/api'
import { formatCurrency, formatDate } from '../lib/utils'
import LoadingSpinner from '../components/LoadingSpinner'

interface SubscriptionTier {
  id: string
  name: string
  price: number
  billing_cycle: 'monthly' | 'yearly'
  features: string[]
  limits: {
    text_requests: number
    image_requests: number
    code_requests: number
    chat_requests: number
  }
  popular?: boolean
  icon: React.ComponentType<any>
  color: string
}

const subscriptionTiers: SubscriptionTier[] = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    billing_cycle: 'monthly',
    features: [
      '100 text generations/month',
      'Basic Llama models',
      'Community support',
      'Standard processing speed'
    ],
    limits: {
      text_requests: 100,
      image_requests: 0,
      code_requests: 0,
      chat_requests: 0
    },
    icon: StarIcon,
    color: 'gray'
  },
  {
    id: 'basic',
    name: 'Basic',
    price: 9.99,
    billing_cycle: 'monthly',
    features: [
      '1,000 text generations/month',
      '500 code generations/month',
      'Advanced Llama models',
      'Email support',
      'Priority processing'
    ],
    limits: {
      text_requests: 1000,
      image_requests: 0,
      code_requests: 500,
      chat_requests: 200
    },
    icon: BoltIcon,
    color: 'yellow'
  },
  {
    id: 'standard',
    name: 'Standard',
    price: 29.99,
    billing_cycle: 'monthly',
    popular: true,
    features: [
      '5,000 text generations/month',
      '2,000 code generations/month',
      '500 image generations/month',
      'All AI models access',
      'Priority support',
      'Advanced analytics'
    ],
    limits: {
      text_requests: 5000,
      image_requests: 500,
      code_requests: 2000,
      chat_requests: 1000
    },
    icon: RocketLaunchIcon,
    color: 'green'
  },
  {
    id: 'advanced',
    name: 'Advanced',
    price: 79.99,
    billing_cycle: 'monthly',
    features: [
      '20,000 text generations/month',
      '10,000 code generations/month',
      '5,000 image generations/month',
      'Custom model fine-tuning',
      'API access',
      'Dedicated support',
      'Custom integrations'
    ],
    limits: {
      text_requests: 20000,
      image_requests: 5000,
      code_requests: 10000,
      chat_requests: 5000
    },
    icon: BuildingOfficeIcon,
    color: 'blue'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 199.99,
    billing_cycle: 'monthly',
    features: [
      'Unlimited generations',
      'Custom AI models',
      'White-label solution',
      'Dedicated infrastructure',
      '24/7 phone support',
      'Custom SLA',
      'On-premise deployment'
    ],
    limits: {
      text_requests: -1,
      image_requests: -1,
      code_requests: -1,
      chat_requests: -1
    },
    icon: BuildingOfficeIcon,
    color: 'purple'
  }
]

export default function BillingPage() {
  const [selectedTier, setSelectedTier] = useState<string>('')
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const queryClient = useQueryClient()

  // Fetch current subscription
  const { data: subscription, isLoading: subscriptionLoading } = useQuery({
    queryKey: ['subscription'],
    queryFn: () => billingApi.getSubscription(),
  })

  // Fetch billing history
  const { data: billingHistory, isLoading: historyLoading } = useQuery({
    queryKey: ['billing-history'],
    queryFn: () => billingApi.getBillingHistory(),
  })

  // Fetch usage data
  const { data: usage, isLoading: usageLoading } = useQuery({
    queryKey: ['usage'],
    queryFn: () => billingApi.getUsage(),
  })

  // Upgrade subscription mutation
  const upgradeMutation = useMutation({
    mutationFn: (tierId: string) => billingApi.upgradeSubscription(tierId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] })
      setShowUpgradeModal(false)
      setSelectedTier('')
    },
  })

  // Cancel subscription mutation
  const cancelMutation = useMutation({
    mutationFn: () => billingApi.cancelSubscription(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['subscription'] })
    },
  })

  const handleUpgrade = (tierId: string) => {
    setSelectedTier(tierId)
    setShowUpgradeModal(true)
  }

  const confirmUpgrade = () => {
    if (selectedTier) {
      upgradeMutation.mutate(selectedTier)
    }
  }

  const getTierColor = (color: string) => {
    const colors = {
      gray: 'border-gray-200 bg-gray-50',
      yellow: 'border-yellow-200 bg-yellow-50',
      green: 'border-green-200 bg-green-50',
      blue: 'border-blue-200 bg-blue-50',
      purple: 'border-purple-200 bg-purple-50',
    }
    return colors[color as keyof typeof colors] || colors.gray
  }

  const getIconColor = (color: string) => {
    const colors = {
      gray: 'text-gray-600',
      yellow: 'text-yellow-600',
      green: 'text-green-600',
      blue: 'text-blue-600',
      purple: 'text-purple-600',
    }
    return colors[color as keyof typeof colors] || colors.gray
  }

  if (subscriptionLoading || historyLoading || usageLoading) {
    return <LoadingSpinner />
  }

  const currentTier = subscription?.data?.tier || 'free'
  const currentUsage = usage?.data || {}

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Billing & Subscription</h1>
            <p className="mt-2 text-gray-600">
              Manage your subscription, view usage, and billing history
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <p className="text-sm text-gray-500">Current Plan</p>
              <p className="text-lg font-semibold text-gray-900 capitalize">
                {currentTier}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Current Usage Overview */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Usage</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600">Text Generations</p>
                <p className="text-2xl font-bold text-blue-900">
                  {currentUsage.text_requests || 0}
                </p>
              </div>
              <DocumentTextIcon className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2">
              <div className="bg-blue-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{
                    width: `${Math.min(100, ((currentUsage.text_requests || 0) / (subscriptionTiers.find(t => t.id === currentTier)?.limits.text_requests || 1)) * 100)}%`
                  }}
                ></div>
              </div>
              <p className="text-xs text-blue-600 mt-1">
                of {subscriptionTiers.find(t => t.id === currentTier)?.limits.text_requests === -1 ? 'unlimited' : subscriptionTiers.find(t => t.id === currentTier)?.limits.text_requests} limit
              </p>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600">Image Generations</p>
                <p className="text-2xl font-bold text-green-900">
                  {currentUsage.image_requests || 0}
                </p>
              </div>
              <CreditCardIcon className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <div className="bg-green-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full"
                  style={{
                    width: `${Math.min(100, ((currentUsage.image_requests || 0) / (subscriptionTiers.find(t => t.id === currentTier)?.limits.image_requests || 1)) * 100)}%`
                  }}
                ></div>
              </div>
              <p className="text-xs text-green-600 mt-1">
                of {subscriptionTiers.find(t => t.id === currentTier)?.limits.image_requests === -1 ? 'unlimited' : subscriptionTiers.find(t => t.id === currentTier)?.limits.image_requests || 0} limit
              </p>
            </div>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600">Code Generations</p>
                <p className="text-2xl font-bold text-purple-900">
                  {currentUsage.code_requests || 0}
                </p>
              </div>
              <BoltIcon className="h-8 w-8 text-purple-500" />
            </div>
            <div className="mt-2">
              <div className="bg-purple-200 rounded-full h-2">
                <div
                  className="bg-purple-600 h-2 rounded-full"
                  style={{
                    width: `${Math.min(100, ((currentUsage.code_requests || 0) / (subscriptionTiers.find(t => t.id === currentTier)?.limits.code_requests || 1)) * 100)}%`
                  }}
                ></div>
              </div>
              <p className="text-xs text-purple-600 mt-1">
                of {subscriptionTiers.find(t => t.id === currentTier)?.limits.code_requests === -1 ? 'unlimited' : subscriptionTiers.find(t => t.id === currentTier)?.limits.code_requests || 0} limit
              </p>
            </div>
          </div>

          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-orange-600">Chat Messages</p>
                <p className="text-2xl font-bold text-orange-900">
                  {currentUsage.chat_requests || 0}
                </p>
              </div>
              <CalendarIcon className="h-8 w-8 text-orange-500" />
            </div>
            <div className="mt-2">
              <div className="bg-orange-200 rounded-full h-2">
                <div
                  className="bg-orange-600 h-2 rounded-full"
                  style={{
                    width: `${Math.min(100, ((currentUsage.chat_requests || 0) / (subscriptionTiers.find(t => t.id === currentTier)?.limits.chat_requests || 1)) * 100)}%`
                  }}
                ></div>
              </div>
              <p className="text-xs text-orange-600 mt-1">
                of {subscriptionTiers.find(t => t.id === currentTier)?.limits.chat_requests === -1 ? 'unlimited' : subscriptionTiers.find(t => t.id === currentTier)?.limits.chat_requests || 0} limit
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Plans */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">Subscription Plans</h2>
          <div className="flex items-center space-x-2">
            <span className={`text-sm ${billingCycle === 'monthly' ? 'text-gray-900' : 'text-gray-500'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                billingCycle === 'yearly' ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  billingCycle === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-sm ${billingCycle === 'yearly' ? 'text-gray-900' : 'text-gray-500'}`}>
              Yearly
            </span>
            {billingCycle === 'yearly' && (
              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                Save 20%
              </span>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {subscriptionTiers.map((tier) => {
            const Icon = tier.icon
            const isCurrentTier = currentTier === tier.id
            const yearlyPrice = tier.price * 12 * 0.8 // 20% discount for yearly
            const displayPrice = billingCycle === 'yearly' ? yearlyPrice : tier.price

            return (
              <div
                key={tier.id}
                className={`relative border-2 rounded-lg p-6 ${
                  tier.popular
                    ? 'border-primary-500 bg-primary-50'
                    : isCurrentTier
                      ? 'border-green-500 bg-green-50'
                      : getTierColor(tier.color)
                } ${tier.popular ? 'scale-105' : ''}`}
              >
                {tier.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-xs font-medium">
                      Most Popular
                    </span>
                  </div>
                )}

                {isCurrentTier && (
                  <div className="absolute -top-3 right-3">
                    <CheckCircleIcon className="h-6 w-6 text-green-500" />
                  </div>
                )}

                <div className="text-center">
                  <Icon className={`h-8 w-8 mx-auto mb-3 ${getIconColor(tier.color)}`} />
                  <h3 className="text-lg font-semibold text-gray-900">{tier.name}</h3>
                  <div className="mt-2">
                    <span className="text-3xl font-bold text-gray-900">
                      {tier.price === 0 ? 'Free' : formatCurrency(displayPrice)}
                    </span>
                    {tier.price > 0 && (
                      <span className="text-gray-500">
                        /{billingCycle === 'yearly' ? 'year' : 'month'}
                      </span>
                    )}
                  </div>
                  {billingCycle === 'yearly' && tier.price > 0 && (
                    <p className="text-sm text-green-600 mt-1">
                      Save {formatCurrency(tier.price * 12 - yearlyPrice)}/year
                    </p>
                  )}
                </div>

                <ul className="mt-6 space-y-3">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckIcon className="h-4 w-4 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <div className="mt-6">
                  {isCurrentTier ? (
                    <div className="flex items-center justify-center py-2 px-4 border border-green-300 rounded-lg bg-green-100">
                      <CheckCircleIcon className="h-4 w-4 text-green-600 mr-2" />
                      <span className="text-sm font-medium text-green-800">Current Plan</span>
                    </div>
                  ) : tier.id === 'free' ? (
                    <button
                      onClick={() => handleUpgrade(tier.id)}
                      disabled={currentTier !== 'free'}
                      className="w-full py-2 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {currentTier === 'free' ? 'Current Plan' : 'Downgrade'}
                    </button>
                  ) : (
                    <button
                      onClick={() => handleUpgrade(tier.id)}
                      className={`w-full py-2 px-4 rounded-lg text-sm font-medium ${
                        tier.popular
                          ? 'bg-primary-600 text-white hover:bg-primary-700'
                          : 'bg-gray-900 text-white hover:bg-gray-800'
                      }`}
                    >
                      {subscriptionTiers.findIndex(t => t.id === currentTier) < subscriptionTiers.findIndex(t => t.id === tier.id)
                        ? 'Upgrade'
                        : 'Change Plan'
                      }
                    </button>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
