import React from 'react'
import { 
  CpuChipIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  InformationCircleIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline'

interface ModelInfo {
  id: string
  name: string
  type: 'text' | 'image' | 'code' | 'chat'
  description: string
  tier_required: string
  memory_usage: number
  available: boolean
  pricing?: {
    per_request: number
    per_token?: number
  }
}

interface ModelSelectorProps {
  models: ModelInfo[]
  selectedModel: string
  onModelSelect: (modelId: string) => void
  userTier?: string
  disabled?: boolean
  filterType?: 'text' | 'image' | 'code' | 'chat'
}

export default function ModelSelector({
  models,
  selectedModel,
  onModelSelect,
  userTier = 'free',
  disabled = false,
  filterType
}: ModelSelectorProps) {
  const filteredModels = filterType 
    ? models.filter(model => model.type === filterType)
    : models

  const canUseModel = (model: ModelInfo) => {
    const tierHierarchy = ['free', 'basic', 'standard', 'advanced', 'enterprise']
    const userTierIndex = tierHierarchy.indexOf(userTier.toLowerCase())
    const requiredTierIndex = tierHierarchy.indexOf(model.tier_required.toLowerCase())
    
    return userTierIndex >= requiredTierIndex && model.available
  }

  const getTierBadgeColor = (tier: string) => {
    const colors = {
      free: 'bg-gray-100 text-gray-800',
      basic: 'bg-yellow-100 text-yellow-800',
      standard: 'bg-green-100 text-green-800',
      advanced: 'bg-blue-100 text-blue-800',
      enterprise: 'bg-purple-100 text-purple-800',
    }
    return colors[tier.toLowerCase() as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  const getTypeIcon = (type: string) => {
    const icons = {
      text: '📝',
      image: '🖼️',
      code: '💻',
      chat: '💬',
    }
    return icons[type as keyof typeof icons] || '🤖'
  }

  const formatMemoryUsage = (mb: number) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)} GB`
    }
    return `${mb} MB`
  }

  const formatPrice = (pricing?: { per_request: number; per_token?: number }) => {
    if (!pricing) return 'Free'
    
    if (pricing.per_token) {
      return `$${pricing.per_token.toFixed(4)}/token`
    }
    
    return `$${pricing.per_request.toFixed(3)}/request`
  }

  if (filteredModels.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <CpuChipIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
        <p>No models available for this type</p>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      {filteredModels.map((model) => {
        const canUse = canUseModel(model)
        const isSelected = selectedModel === model.id
        
        return (
          <div
            key={model.id}
            className={`
              relative border-2 rounded-lg p-4 transition-all duration-200 cursor-pointer
              ${isSelected 
                ? 'border-primary-500 bg-primary-50' 
                : canUse 
                  ? 'border-gray-200 hover:border-gray-300 bg-white' 
                  : 'border-gray-100 bg-gray-50 opacity-60 cursor-not-allowed'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            onClick={() => canUse && !disabled && onModelSelect(model.id)}
          >
            {/* Model Header */}
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center space-x-3">
                <div className="text-2xl">{getTypeIcon(model.type)}</div>
                <div>
                  <h3 className={`font-semibold ${isSelected ? 'text-primary-900' : 'text-gray-900'}`}>
                    {model.name}
                  </h3>
                  <p className={`text-sm ${isSelected ? 'text-primary-700' : 'text-gray-600'}`}>
                    {model.description}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                {/* Availability Status */}
                {model.available ? (
                  <CheckCircleIcon className="h-5 w-5 text-green-500" title="Available" />
                ) : (
                  <XCircleIcon className="h-5 w-5 text-red-500" title="Unavailable" />
                )}
                
                {/* Access Lock */}
                {!canUse && (
                  <LockClosedIcon className="h-5 w-5 text-gray-400" title="Upgrade Required" />
                )}
              </div>
            </div>

            {/* Model Details */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              {/* Tier Required */}
              <div>
                <span className="text-gray-500 block">Tier Required</span>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTierBadgeColor(model.tier_required)}`}>
                  {model.tier_required.charAt(0).toUpperCase() + model.tier_required.slice(1)}
                </span>
              </div>
              
              {/* Memory Usage */}
              <div>
                <span className="text-gray-500 block">Memory</span>
                <span className="font-medium text-gray-900">
                  {formatMemoryUsage(model.memory_usage)}
                </span>
              </div>
              
              {/* Pricing */}
              <div>
                <span className="text-gray-500 block">Pricing</span>
                <span className="font-medium text-gray-900">
                  {formatPrice(model.pricing)}
                </span>
              </div>
              
              {/* Type */}
              <div>
                <span className="text-gray-500 block">Type</span>
                <span className="font-medium text-gray-900 capitalize">
                  {model.type}
                </span>
              </div>
            </div>

            {/* Upgrade Notice */}
            {!canUse && (
              <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center text-yellow-800">
                  <InformationCircleIcon className="h-4 w-4 mr-2" />
                  <span className="text-sm">
                    Requires {model.tier_required.charAt(0).toUpperCase() + model.tier_required.slice(1)} plan or higher
                  </span>
                </div>
              </div>
            )}

            {/* Selection Indicator */}
            {isSelected && (
              <div className="absolute top-2 right-2">
                <div className="w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
            )}
          </div>
        )
      })}
    </div>
  )
}
