# EliteForge AI - Backend Dependencies
# Core FastAPI and web framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Database
sqlalchemy==2.0.23
asyncpg==0.29.0
alembic==1.12.1
psycopg2-binary==2.9.9

# Redis
redis==5.0.1
aioredis==2.0.1

# Pydantic and validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Authentication and security
python-jose==3.3.0
bcrypt==4.1.2
cryptography==41.0.8

# HTTP client
httpx==0.25.2
aiohttp==3.9.1

# AI/ML libraries
torch==2.1.1
transformers==4.36.0
diffusers==0.24.0
accelerate==0.25.0
safetensors==0.4.1
huggingface-hub==0.19.4
sentence-transformers==2.2.2

# Image processing
Pillow==10.1.0
opencv-python==********

# Payment processing
stripe==7.8.0

# File storage
boto3==1.34.0
minio==7.2.0

# Email
fastapi-mail==1.4.1
jinja2==3.1.2

# Monitoring and logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Task queue
celery==5.3.4
redis==5.0.1

# Rate limiting
slowapi==0.1.9

# Configuration
python-dotenv==1.0.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2
faker==20.1.0

# Development tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8

# Utilities
python-slugify==8.0.1
pytz==2023.3
click==8.1.7
rich==13.7.0
typer==0.9.0

# WebSocket support
websockets==12.0

# Background tasks
apscheduler==3.10.4

# Data validation and serialization
marshmallow==3.20.1
orjson==3.9.10

# Caching
aiocache==0.12.2

# Metrics and monitoring
psutil==5.9.6
py-cpuinfo==9.0.0

# File handling
aiofiles==23.2.1
python-magic==0.4.27

# Networking
dnspython==2.4.2

# Compression
gzip==1.0.0

# Date/time utilities
python-dateutil==2.8.2
arrow==1.3.0

# JSON Web Tokens
pyjwt==2.8.0

# Environment and process management
supervisor==4.2.5

# Database migrations
yoyo-migrations==8.2.0

# API documentation
fastapi-users==12.1.2

# Async utilities
asyncio-mqtt==0.16.1

# Performance monitoring
py-spy==0.3.14

# Memory profiling
memory-profiler==0.61.0

# Code quality
bandit==1.7.5
safety==2.3.5

# Deployment
gunicorn==21.2.0
gevent==23.9.1
