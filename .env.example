# EliteForge AI - Environment Configuration Template
# Copy this file to .env and update the values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
APP_NAME=EliteForge AI
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# SECURITY
# =============================================================================
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql+asyncpg://eliteforge_user:eliteforge_password@localhost:5432/eliteforge
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://:eliteforge_redis_password@localhost:6379/0
REDIS_PASSWORD=eliteforge_redis_password
REDIS_DB=0

# =============================================================================
# CORS AND SECURITY
# =============================================================================
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://yourdomain.com
ALLOWED_HOSTS=localhost,127.0.0.1,yourdomain.com

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# =============================================================================
# STRIPE PAYMENT CONFIGURATION
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PRICE_BASIC=price_your_basic_price_id
STRIPE_PRICE_PRO=price_your_pro_price_id
STRIPE_PRICE_ENTERPRISE=price_your_enterprise_price_id

# =============================================================================
# AI SERVICES CONFIGURATION
# =============================================================================
HUGGINGFACE_API_KEY=hf_your_huggingface_api_key_here
OPENAI_API_KEY=sk-your_openai_api_key_here
REPLICATE_API_TOKEN=r8_your_replicate_token_here

# =============================================================================
# MODEL CONFIGURATION
# =============================================================================
DEFAULT_TEXT_MODEL=microsoft/DialoGPT-medium
DEFAULT_IMAGE_MODEL=runwayml/stable-diffusion-v1-5
MODEL_CACHE_DIR=./models
MAX_MODEL_MEMORY=8192

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
STORAGE_TYPE=minio
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=eliteforge-files
MINIO_SECURE=false

# AWS S3 Configuration (if using S3 instead of MinIO)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
FROM_EMAIL=<EMAIL>
FROM_NAME=EliteForge AI

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
SENTRY_DSN=https://your-sentry-dsn-here
ENABLE_METRICS=true
METRICS_PORT=9090

# =============================================================================
# SUBSCRIPTION LIMITS
# =============================================================================
FREE_TIER_REQUESTS=100
BASIC_TIER_REQUESTS=1000
PRO_TIER_REQUESTS=10000
ENTERPRISE_TIER_REQUESTS=-1

# =============================================================================
# PRICING (in cents)
# =============================================================================
BASIC_TIER_PRICE=999
PRO_TIER_PRICE=2999
ENTERPRISE_TIER_PRICE=9999
TEXT_REQUEST_PRICE=1
IMAGE_REQUEST_PRICE=5
CUSTOM_REQUEST_PRICE=10

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
VITE_API_BASE_URL=http://localhost:8000
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
VITE_APP_NAME=EliteForge AI
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Set to true to enable development features
ENABLE_SWAGGER_UI=true
ENABLE_REDOC=true
ENABLE_QUERY_DEVTOOLS=true

# =============================================================================
# PRODUCTION OVERRIDES
# =============================================================================
# Uncomment and modify these for production deployment
# ENVIRONMENT=production
# DEBUG=false
# LOG_LEVEL=WARNING
# ALLOWED_ORIGINS=https://yourdomain.com
# ALLOWED_HOSTS=yourdomain.com
# DATABASE_URL=postgresql+asyncpg://user:password@prod-db:5432/eliteforge
# REDIS_URL=redis://prod-redis:6379/0
# ENABLE_SWAGGER_UI=false
# ENABLE_REDOC=false

# =============================================================================
# DOCKER COMPOSE OVERRIDES
# =============================================================================
# These are used by docker-compose.yml
POSTGRES_DB=eliteforge
POSTGRES_USER=eliteforge_user
POSTGRES_PASSWORD=eliteforge_password
REDIS_PASSWORD=eliteforge_redis_password

# =============================================================================
# SSL/TLS CONFIGURATION (Production)
# =============================================================================
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/key.pem
# SSL_CA_PATH=/path/to/ssl/ca.pem

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
# BACKUP_S3_BUCKET=your-backup-bucket
# BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# =============================================================================
# ANALYTICS AND TRACKING
# =============================================================================
# GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
# MIXPANEL_TOKEN=your_mixpanel_token
# HOTJAR_ID=your_hotjar_id

# =============================================================================
# SOCIAL AUTH (Optional)
# =============================================================================
# GOOGLE_CLIENT_ID=your_google_client_id
# GOOGLE_CLIENT_SECRET=your_google_client_secret
# GITHUB_CLIENT_ID=your_github_client_id
# GITHUB_CLIENT_SECRET=your_github_client_secret

# =============================================================================
# WEBHOOK ENDPOINTS
# =============================================================================
# SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
# DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/webhook/url

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# ENABLE_BETA_FEATURES=false
# ENABLE_EXPERIMENTAL_MODELS=false
# ENABLE_WHITE_LABEL=false
# ENABLE_API_MARKETPLACE=false
