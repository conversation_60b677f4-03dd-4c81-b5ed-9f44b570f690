#!/bin/bash

# EliteForge AI - Development Environment Setup Script
# This script sets up the complete development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    local missing_deps=()
    
    if ! command_exists docker; then
        missing_deps+=("docker")
    fi
    
    if ! command_exists docker-compose; then
        missing_deps+=("docker-compose")
    fi
    
    if ! command_exists node; then
        missing_deps+=("node")
    fi
    
    if ! command_exists npm; then
        missing_deps+=("npm")
    fi
    
    if ! command_exists python3; then
        missing_deps+=("python3")
    fi
    
    if ! command_exists pip3; then
        missing_deps+=("pip3")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing required dependencies: ${missing_deps[*]}"
        log_info "Please install the missing dependencies and run this script again."
        exit 1
    fi
    
    log_success "All prerequisites are installed"
}

# Setup environment file
setup_environment() {
    log_info "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            log_success "Created .env file from .env.example"
            log_warning "Please update the .env file with your actual configuration values"
        else
            log_error ".env.example file not found"
            exit 1
        fi
    else
        log_info ".env file already exists, skipping..."
    fi
}

# Generate secret key
generate_secret_key() {
    log_info "Generating secret key..."
    
    if command_exists openssl; then
        SECRET_KEY=$(openssl rand -hex 32)
    elif command_exists python3; then
        SECRET_KEY=$(python3 -c "import secrets; print(secrets.token_hex(32))")
    else
        log_warning "Could not generate secret key automatically. Please set SECRET_KEY in .env manually"
        return
    fi
    
    # Update .env file with generated secret key
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/SECRET_KEY=your-super-secret-key-change-this-in-production/SECRET_KEY=$SECRET_KEY/" .env
    else
        # Linux
        sed -i "s/SECRET_KEY=your-super-secret-key-change-this-in-production/SECRET_KEY=$SECRET_KEY/" .env
    fi
    
    log_success "Generated and set secret key"
}

# Setup backend
setup_backend() {
    log_info "Setting up backend..."
    
    cd backend
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        log_info "Creating Python virtual environment..."
        python3 -m venv venv
        log_success "Created virtual environment"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Upgrade pip
    log_info "Upgrading pip..."
    pip install --upgrade pip
    
    # Install dependencies
    log_info "Installing Python dependencies..."
    pip install -r requirements.txt
    
    log_success "Backend setup completed"
    cd ..
}

# Setup frontend
setup_frontend() {
    log_info "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    log_info "Installing Node.js dependencies..."
    npm install
    
    log_success "Frontend setup completed"
    cd ..
}

# Setup Docker environment
setup_docker() {
    log_info "Setting up Docker environment..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Build Docker images
    log_info "Building Docker images..."
    docker-compose build
    
    log_success "Docker environment setup completed"
}

# Initialize database
init_database() {
    log_info "Initializing database..."
    
    # Start database services
    docker-compose up -d postgres redis
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Run database migrations
    log_info "Running database migrations..."
    cd backend
    source venv/bin/activate
    alembic upgrade head
    cd ..
    
    log_success "Database initialized"
}

# Create initial admin user
create_admin_user() {
    log_info "Creating initial admin user..."
    
    read -p "Enter admin email: " ADMIN_EMAIL
    read -s -p "Enter admin password: " ADMIN_PASSWORD
    echo
    
    cd backend
    source venv/bin/activate
    python -c "
import asyncio
from app.core.database import get_db_session
from app.services.user_service import UserService
from app.db.models import UserRole

async def create_admin():
    async with get_db_session() as db:
        user_service = UserService(db)
        await user_service.create_user(
            email='$ADMIN_EMAIL',
            username='admin',
            password='$ADMIN_PASSWORD',
            full_name='Administrator',
            role=UserRole.SUPER_ADMIN,
            is_verified=True
        )
        print('Admin user created successfully')

asyncio.run(create_admin())
"
    cd ..
    
    log_success "Admin user created"
}

# Setup AI models
setup_ai_models() {
    log_info "Setting up AI models..."
    
    # Create models directory
    mkdir -p models
    
    log_info "AI models directory created. Models will be downloaded on first use."
    log_success "AI models setup completed"
}

# Verify installation
verify_installation() {
    log_info "Verifying installation..."
    
    # Start all services
    docker-compose up -d
    
    # Wait for services to start
    sleep 30
    
    # Check backend health
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        log_success "Backend is running and healthy"
    else
        log_error "Backend health check failed"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 >/dev/null 2>&1; then
        log_success "Frontend is running"
    else
        log_error "Frontend health check failed"
    fi
    
    # Check database
    if docker-compose exec -T postgres pg_isready -U eliteforge_user -d eliteforge >/dev/null 2>&1; then
        log_success "Database is running"
    else
        log_error "Database health check failed"
    fi
    
    # Check Redis
    if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
        log_success "Redis is running"
    else
        log_error "Redis health check failed"
    fi
}

# Main setup function
main() {
    log_info "Starting EliteForge AI development environment setup..."
    
    check_prerequisites
    setup_environment
    generate_secret_key
    setup_backend
    setup_frontend
    setup_docker
    init_database
    setup_ai_models
    
    # Ask if user wants to create admin user
    read -p "Do you want to create an admin user? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_admin_user
    fi
    
    verify_installation
    
    log_success "EliteForge AI development environment setup completed!"
    echo
    log_info "You can now access:"
    log_info "  - Frontend: http://localhost:3000"
    log_info "  - Backend API: http://localhost:8000"
    log_info "  - API Documentation: http://localhost:8000/docs"
    log_info "  - Admin Panel: http://localhost:3000/admin"
    log_info "  - Grafana Dashboard: http://localhost:3001 (admin/admin123)"
    log_info "  - Flower (Celery): http://localhost:5555"
    echo
    log_info "To stop all services: docker-compose down"
    log_info "To view logs: docker-compose logs -f"
    log_info "To restart services: docker-compose restart"
}

# Run main function
main "$@"
