"""
EliteForge AI - API Router
Main API router that includes all endpoint modules
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    users,
    subscriptions,
    ai_requests,
    billing,
    admin,
    analytics,
    health
)

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

api_router.include_router(
    subscriptions.router,
    prefix="/subscriptions",
    tags=["subscriptions"]
)

api_router.include_router(
    ai_requests.router,
    prefix="/ai",
    tags=["ai-requests"]
)

api_router.include_router(
    billing.router,
    prefix="/billing",
    tags=["billing"]
)

api_router.include_router(
    admin.router,
    prefix="/admin",
    tags=["admin"]
)

api_router.include_router(
    analytics.router,
    prefix="/analytics",
    tags=["analytics"]
)

api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)
