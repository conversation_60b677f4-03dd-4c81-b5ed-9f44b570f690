"""
EliteForge AI - Invoice and Billing History Service
Generate invoices, manage billing history, and provide financial reporting
"""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

from jinja2 import Environment, FileSystemLoader
from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.config import settings
from app.core.exceptions import CustomException
from app.db.models import (
    CreditTransaction, Invoice, InvoiceStatus, Payment, Subscription, 
    SubscriptionTier, User, UsageRecord
)

logger = logging.getLogger(__name__)


class InvoiceService:
    """Comprehensive invoice generation and billing history management"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        
        # Initialize Jinja2 for invoice templates
        self.template_env = Environment(
            loader=FileSystemLoader("app/templates/invoices")
        )
        
        # Tax rates by region (example)
        self.tax_rates = {
            "US": 0.08,      # 8% average US sales tax
            "EU": 0.20,      # 20% EU VAT
            "UK": 0.20,      # 20% UK VAT
            "CA": 0.13,      # 13% Canadian HST
            "AU": 0.10,      # 10% Australian GST
            "default": 0.00  # No tax for other regions
        }
        
        # Invoice number format
        self.invoice_prefix = "EF"  # EliteForge
    
    async def generate_subscription_invoice(
        self,
        subscription_id: UUID,
        billing_period_start: datetime,
        billing_period_end: datetime
    ) -> Invoice:
        """Generate invoice for subscription billing period"""
        try:
            # Get subscription with user details
            result = await self.db.execute(
                select(Subscription)
                .where(Subscription.id == subscription_id)
                .options(selectinload(Subscription.user))
            )
            
            subscription = result.scalar_one_or_none()
            if not subscription:
                raise CustomException(
                    status_code=404,
                    error_code="SUBSCRIPTION_NOT_FOUND",
                    message="Subscription not found"
                )
            
            user = subscription.user
            
            # Calculate subscription cost
            tier_pricing = self._get_tier_pricing(subscription.tier)
            subtotal_cents = tier_pricing["monthly_price_cents"]
            
            # Get usage records for the billing period
            usage_records = await self._get_usage_records(
                user.id, billing_period_start, billing_period_end
            )
            
            # Calculate overage charges
            overage_cents = await self._calculate_overage_charges(
                subscription, usage_records
            )
            
            # Calculate tax
            tax_rate = self._get_tax_rate(user.country or "default")
            tax_cents = int((subtotal_cents + overage_cents) * tax_rate)
            
            total_cents = subtotal_cents + overage_cents + tax_cents
            
            # Generate invoice number
            invoice_number = await self._generate_invoice_number()
            
            # Create invoice
            invoice = Invoice(
                id=uuid4(),
                user_id=user.id,
                subscription_id=subscription_id,
                invoice_number=invoice_number,
                status=InvoiceStatus.PENDING,
                subtotal_cents=subtotal_cents,
                tax_cents=tax_cents,
                total_cents=total_cents,
                currency="USD",
                billing_period_start=billing_period_start,
                billing_period_end=billing_period_end,
                due_date=datetime.utcnow() + timedelta(days=30),
                created_at=datetime.utcnow(),
                metadata={
                    "tier": subscription.tier.value,
                    "overage_cents": overage_cents,
                    "tax_rate": tax_rate,
                    "usage_records_count": len(usage_records)
                }
            )
            
            self.db.add(invoice)
            await self.db.commit()
            await self.db.refresh(invoice)
            
            # Generate PDF invoice
            await self._generate_invoice_pdf(invoice)
            
            logger.info(f"Generated subscription invoice {invoice_number} for user {user.id}")
            
            return invoice
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Subscription invoice generation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="INVOICE_GENERATION_FAILED",
                message=f"Failed to generate invoice: {str(e)}"
            )
    
    async def generate_credit_purchase_invoice(
        self,
        user_id: UUID,
        credit_transaction_id: UUID,
        payment_id: UUID
    ) -> Invoice:
        """Generate invoice for credit purchase"""
        try:
            # Get transaction details
            result = await self.db.execute(
                select(CreditTransaction)
                .where(CreditTransaction.id == credit_transaction_id)
                .options(selectinload(CreditTransaction.user))
            )
            
            transaction = result.scalar_one_or_none()
            if not transaction:
                raise CustomException(
                    status_code=404,
                    error_code="TRANSACTION_NOT_FOUND",
                    message="Credit transaction not found"
                )
            
            user = transaction.user
            
            # Get payment details
            payment_result = await self.db.execute(
                select(Payment).where(Payment.id == payment_id)
            )
            payment = payment_result.scalar_one_or_none()
            
            # Calculate amounts
            subtotal_cents = transaction.amount_cents
            tax_rate = self._get_tax_rate(user.country or "default")
            tax_cents = int(subtotal_cents * tax_rate)
            total_cents = subtotal_cents + tax_cents
            
            # Generate invoice
            invoice_number = await self._generate_invoice_number()
            
            invoice = Invoice(
                id=uuid4(),
                user_id=user_id,
                payment_id=payment_id,
                invoice_number=invoice_number,
                status=InvoiceStatus.PAID if payment and payment.status == "succeeded" else InvoiceStatus.PENDING,
                subtotal_cents=subtotal_cents,
                tax_cents=tax_cents,
                total_cents=total_cents,
                currency="USD",
                due_date=datetime.utcnow() + timedelta(days=1),  # Credit purchases due immediately
                paid_at=payment.paid_at if payment and payment.paid_at else None,
                created_at=datetime.utcnow(),
                metadata={
                    "type": "credit_purchase",
                    "credits_purchased": transaction.amount_cents,
                    "transaction_id": str(credit_transaction_id)
                }
            )
            
            self.db.add(invoice)
            await self.db.commit()
            await self.db.refresh(invoice)
            
            # Generate PDF
            await self._generate_invoice_pdf(invoice)
            
            logger.info(f"Generated credit purchase invoice {invoice_number} for user {user_id}")
            
            return invoice
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Credit purchase invoice generation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="INVOICE_GENERATION_FAILED",
                message=f"Failed to generate credit purchase invoice: {str(e)}"
            )
    
    async def get_user_invoices(
        self,
        user_id: UUID,
        limit: int = 50,
        offset: int = 0,
        status_filter: Optional[InvoiceStatus] = None
    ) -> Dict[str, Any]:
        """Get user's invoice history"""
        try:
            # Build query
            query = select(Invoice).where(Invoice.user_id == user_id)
            
            if status_filter:
                query = query.where(Invoice.status == status_filter)
            
            query = query.order_by(desc(Invoice.created_at)).limit(limit).offset(offset)
            
            # Get invoices
            result = await self.db.execute(query)
            invoices = result.scalars().all()
            
            # Get total count
            count_query = select(func.count(Invoice.id)).where(Invoice.user_id == user_id)
            if status_filter:
                count_query = count_query.where(Invoice.status == status_filter)
            
            count_result = await self.db.execute(count_query)
            total_count = count_result.scalar()
            
            # Format response
            invoice_list = []
            for invoice in invoices:
                invoice_list.append({
                    "id": str(invoice.id),
                    "invoice_number": invoice.invoice_number,
                    "status": invoice.status.value,
                    "subtotal_cents": invoice.subtotal_cents,
                    "tax_cents": invoice.tax_cents,
                    "total_cents": invoice.total_cents,
                    "total_dollars": invoice.total_cents / 100,
                    "currency": invoice.currency,
                    "billing_period_start": invoice.billing_period_start.isoformat() if invoice.billing_period_start else None,
                    "billing_period_end": invoice.billing_period_end.isoformat() if invoice.billing_period_end else None,
                    "due_date": invoice.due_date.isoformat() if invoice.due_date else None,
                    "paid_at": invoice.paid_at.isoformat() if invoice.paid_at else None,
                    "created_at": invoice.created_at.isoformat(),
                    "pdf_url": f"/api/invoices/{invoice.id}/pdf" if invoice.pdf_path else None,
                    "metadata": invoice.metadata
                })
            
            return {
                "invoices": invoice_list,
                "total_count": total_count,
                "has_more": offset + len(invoices) < total_count
            }
            
        except Exception as e:
            logger.error(f"Failed to get user invoices: {str(e)}")
            return {"invoices": [], "total_count": 0, "has_more": False, "error": str(e)}
    
    async def get_billing_summary(self, user_id: UUID) -> Dict[str, Any]:
        """Get comprehensive billing summary"""
        try:
            # Get current month invoices
            current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            
            current_month_result = await self.db.execute(
                select(
                    func.count(Invoice.id).label("invoice_count"),
                    func.sum(Invoice.total_cents).label("total_amount")
                )
                .where(
                    and_(
                        Invoice.user_id == user_id,
                        Invoice.created_at >= current_month_start
                    )
                )
            )
            
            current_month_data = current_month_result.first()
            
            # Get year-to-date totals
            year_start = datetime.utcnow().replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
            
            ytd_result = await self.db.execute(
                select(
                    func.count(Invoice.id).label("invoice_count"),
                    func.sum(Invoice.total_cents).label("total_amount")
                )
                .where(
                    and_(
                        Invoice.user_id == user_id,
                        Invoice.created_at >= year_start
                    )
                )
            )
            
            ytd_data = ytd_result.first()
            
            # Get pending invoices
            pending_result = await self.db.execute(
                select(
                    func.count(Invoice.id).label("pending_count"),
                    func.sum(Invoice.total_cents).label("pending_amount")
                )
                .where(
                    and_(
                        Invoice.user_id == user_id,
                        Invoice.status == InvoiceStatus.PENDING
                    )
                )
            )
            
            pending_data = pending_result.first()
            
            # Get recent invoices
            recent_invoices = await self.get_user_invoices(user_id, limit=5)
            
            return {
                "current_month": {
                    "invoice_count": current_month_data.invoice_count or 0,
                    "total_amount_cents": current_month_data.total_amount or 0,
                    "total_amount_dollars": (current_month_data.total_amount or 0) / 100
                },
                "year_to_date": {
                    "invoice_count": ytd_data.invoice_count or 0,
                    "total_amount_cents": ytd_data.total_amount or 0,
                    "total_amount_dollars": (ytd_data.total_amount or 0) / 100
                },
                "pending": {
                    "invoice_count": pending_data.pending_count or 0,
                    "total_amount_cents": pending_data.pending_amount or 0,
                    "total_amount_dollars": (pending_data.pending_amount or 0) / 100
                },
                "recent_invoices": recent_invoices["invoices"]
            }
            
        except Exception as e:
            logger.error(f"Billing summary generation failed: {str(e)}")
            return {"error": str(e)}
    
    async def mark_invoice_paid(self, invoice_id: UUID, payment_id: UUID = None) -> Invoice:
        """Mark invoice as paid"""
        try:
            result = await self.db.execute(
                select(Invoice).where(Invoice.id == invoice_id)
            )
            
            invoice = result.scalar_one_or_none()
            if not invoice:
                raise CustomException(
                    status_code=404,
                    error_code="INVOICE_NOT_FOUND",
                    message="Invoice not found"
                )
            
            invoice.status = InvoiceStatus.PAID
            invoice.paid_at = datetime.utcnow()
            if payment_id:
                invoice.payment_id = payment_id
            
            await self.db.commit()
            await self.db.refresh(invoice)
            
            logger.info(f"Marked invoice {invoice.invoice_number} as paid")
            
            return invoice
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Failed to mark invoice as paid: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="INVOICE_UPDATE_FAILED",
                message=f"Failed to update invoice: {str(e)}"
            )
    
    def _get_tier_pricing(self, tier: SubscriptionTier) -> Dict[str, Any]:
        """Get pricing for subscription tier"""
        pricing = {
            SubscriptionTier.FREE: {"monthly_price_cents": 0},
            SubscriptionTier.BASIC: {"monthly_price_cents": 1999},    # $19.99
            SubscriptionTier.PRO: {"monthly_price_cents": 4999},     # $49.99
            SubscriptionTier.ENTERPRISE: {"monthly_price_cents": 9999} # $99.99
        }
        
        return pricing.get(tier, {"monthly_price_cents": 0})
    
    def _get_tax_rate(self, country: str) -> float:
        """Get tax rate for country"""
        return self.tax_rates.get(country.upper(), self.tax_rates["default"])
    
    async def _get_usage_records(
        self,
        user_id: UUID,
        start_date: datetime,
        end_date: datetime
    ) -> List[UsageRecord]:
        """Get usage records for billing period"""
        result = await self.db.execute(
            select(UsageRecord)
            .where(
                and_(
                    UsageRecord.user_id == user_id,
                    UsageRecord.created_at >= start_date,
                    UsageRecord.created_at <= end_date
                )
            )
        )
        
        return result.scalars().all()
    
    async def _calculate_overage_charges(
        self,
        subscription: Subscription,
        usage_records: List[UsageRecord]
    ) -> int:
        """Calculate overage charges for subscription"""
        # This would implement overage billing logic
        # For now, return 0 (no overages)
        return 0
    
    async def _generate_invoice_number(self) -> str:
        """Generate unique invoice number"""
        # Get current year and month
        now = datetime.utcnow()
        year_month = now.strftime("%Y%m")
        
        # Get next sequence number for this month
        result = await self.db.execute(
            select(func.count(Invoice.id))
            .where(Invoice.invoice_number.like(f"{self.invoice_prefix}-{year_month}-%"))
        )
        
        sequence = (result.scalar() or 0) + 1
        
        return f"{self.invoice_prefix}-{year_month}-{sequence:04d}"
    
    async def _generate_invoice_pdf(self, invoice: Invoice):
        """Generate PDF for invoice"""
        try:
            # This would use a PDF generation library like WeasyPrint or ReportLab
            # For now, just set a placeholder path
            invoice.pdf_path = f"invoices/{invoice.invoice_number}.pdf"
            await self.db.commit()
            
            logger.info(f"Generated PDF for invoice {invoice.invoice_number}")
            
        except Exception as e:
            logger.error(f"PDF generation failed for invoice {invoice.invoice_number}: {str(e)}")


__all__ = ["InvoiceService"]
