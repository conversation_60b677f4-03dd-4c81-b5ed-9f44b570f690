"""
EliteForge AI - Billing and Usage Tracking Service
Comprehensive usage tracking, pay-per-use credits, and billing management
"""

import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.config import settings
from app.core.exceptions import CustomException
from app.db.models import (
    AIRequest, CreditTransaction, Payment, RequestType, Subscription, 
    SubscriptionTier, User, UsageRecord
)
from app.services.payoneer_service import PayoneerService

logger = logging.getLogger(__name__)


class BillingService:
    """Comprehensive billing and usage tracking service"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.payoneer_service = PayoneerService(db)
        
        # Pay-per-use pricing (in cents)
        self.usage_pricing = {
            RequestType.TEXT_GENERATION: {
                "base_cost": 1,  # $0.01 per request
                "token_cost": 0.001,  # $0.001 per 1000 tokens
                "model_multipliers": {
                    "llama-2-7b": 1.0,
                    "llama-2-13b": 1.5,
                    "code-llama-7b": 1.2,
                    "code-llama-13b": 1.8
                }
            },
            RequestType.IMAGE_GENERATION: {
                "base_cost": 5,  # $0.05 per image
                "resolution_multipliers": {
                    "512x512": 1.0,
                    "768x768": 1.5,
                    "1024x1024": 2.0,
                    "1536x1536": 3.0
                },
                "model_multipliers": {
                    "sd-1.5": 1.0,
                    "sd-2.1": 1.2,
                    "sd-xl": 1.5,
                    "flux-schnell": 2.0,
                    "flux-dev": 2.5
                }
            },
            RequestType.CODE_GENERATION: {
                "base_cost": 3,  # $0.03 per request
                "complexity_multipliers": {
                    "simple": 1.0,
                    "medium": 1.5,
                    "complex": 2.0
                }
            },
            RequestType.CUSTOM_ALGORITHM: {
                "base_cost": 10,  # $0.10 per request
                "processing_time_cost": 0.1  # $0.001 per second
            }
        }
        
        # Credit packages
        self.credit_packages = {
            "starter": {"credits": 1000, "price_cents": 999, "bonus": 0},      # $9.99 for $10 credits
            "standard": {"credits": 2500, "price_cents": 1999, "bonus": 500},  # $19.99 for $25 + $5 bonus
            "premium": {"credits": 5500, "price_cents": 3999, "bonus": 1500},  # $39.99 for $55 + $15 bonus
            "enterprise": {"credits": 12000, "price_cents": 7999, "bonus": 4000} # $79.99 for $120 + $40 bonus
        }
    
    async def calculate_request_cost(
        self,
        request_type: RequestType,
        parameters: Dict[str, Any] = None,
        tokens_used: int = 0,
        processing_time: float = 0
    ) -> int:
        """Calculate cost for AI request in cents"""
        try:
            params = parameters or {}
            pricing = self.usage_pricing.get(request_type, {"base_cost": 1})
            
            base_cost = pricing["base_cost"]
            total_cost = base_cost
            
            if request_type == RequestType.TEXT_GENERATION:
                # Add token-based cost
                if tokens_used > 0:
                    token_cost = (tokens_used / 1000) * pricing.get("token_cost", 0.001) * 100  # Convert to cents
                    total_cost += token_cost
                
                # Apply model multiplier
                model_name = params.get("model_name", "")
                multiplier = pricing.get("model_multipliers", {}).get(model_name, 1.0)
                total_cost *= multiplier
                
            elif request_type == RequestType.IMAGE_GENERATION:
                # Apply resolution multiplier
                width = params.get("width", 512)
                height = params.get("height", 512)
                resolution = f"{width}x{height}"
                resolution_multiplier = pricing.get("resolution_multipliers", {}).get(resolution, 1.0)
                total_cost *= resolution_multiplier
                
                # Apply model multiplier
                model_name = params.get("model_name", "")
                model_multiplier = pricing.get("model_multipliers", {}).get(model_name, 1.0)
                total_cost *= model_multiplier
                
            elif request_type == RequestType.CODE_GENERATION:
                # Apply complexity multiplier
                complexity = params.get("complexity", "simple")
                complexity_multiplier = pricing.get("complexity_multipliers", {}).get(complexity, 1.0)
                total_cost *= complexity_multiplier
                
            elif request_type == RequestType.CUSTOM_ALGORITHM:
                # Add processing time cost
                if processing_time > 0:
                    time_cost = processing_time * pricing.get("processing_time_cost", 0.1) * 100  # Convert to cents
                    total_cost += time_cost
            
            return max(1, int(total_cost))  # Minimum 1 cent
            
        except Exception as e:
            logger.error(f"Cost calculation failed: {str(e)}")
            return 1  # Default to 1 cent
    
    async def get_user_credits(self, user_id: UUID) -> Dict[str, Any]:
        """Get user's current credit balance"""
        try:
            # Calculate total credits from transactions
            result = await self.db.execute(
                select(func.sum(CreditTransaction.amount_cents))
                .where(CreditTransaction.user_id == user_id)
            )
            
            total_credits = result.scalar() or 0
            
            # Get recent transactions
            recent_result = await self.db.execute(
                select(CreditTransaction)
                .where(CreditTransaction.user_id == user_id)
                .order_by(CreditTransaction.created_at.desc())
                .limit(10)
            )
            
            recent_transactions = recent_result.scalars().all()
            
            return {
                "balance_cents": total_credits,
                "balance_dollars": total_credits / 100,
                "recent_transactions": [
                    {
                        "id": str(tx.id),
                        "amount_cents": tx.amount_cents,
                        "amount_dollars": tx.amount_cents / 100,
                        "type": tx.transaction_type,
                        "description": tx.description,
                        "created_at": tx.created_at.isoformat()
                    }
                    for tx in recent_transactions
                ]
            }
            
        except Exception as e:
            logger.error(f"Failed to get user credits: {str(e)}")
            return {"balance_cents": 0, "balance_dollars": 0, "recent_transactions": []}
    
    async def purchase_credits(
        self,
        user_id: UUID,
        package_name: str,
        payment_method_id: str = None
    ) -> Dict[str, Any]:
        """Purchase credit package"""
        try:
            if package_name not in self.credit_packages:
                raise CustomException(
                    status_code=400,
                    error_code="INVALID_CREDIT_PACKAGE",
                    message=f"Invalid credit package: {package_name}"
                )
            
            package = self.credit_packages[package_name]
            total_credits = package["credits"] + package["bonus"]
            price_cents = package["price_cents"]
            
            # Process payment
            payment_result = await self.payoneer_service.process_payment(
                user_id=user_id,
                amount_cents=price_cents,
                description=f"Credit purchase: {package_name} package",
                payment_method_id=payment_method_id,
                metadata={
                    "package": package_name,
                    "credits": total_credits,
                    "bonus_credits": package["bonus"]
                }
            )
            
            # Add credits to user account
            await self._add_credits(
                user_id=user_id,
                amount_cents=total_credits,
                transaction_type="purchase",
                description=f"Credit purchase: {package_name} package ({package['credits']} + {package['bonus']} bonus)",
                reference_id=payment_result["payment_id"]
            )
            
            logger.info(f"User {user_id} purchased {total_credits} credits for ${price_cents/100:.2f}")
            
            return {
                "package": package_name,
                "credits_purchased": package["credits"],
                "bonus_credits": package["bonus"],
                "total_credits": total_credits,
                "amount_paid": price_cents,
                "payment_id": payment_result["payment_id"]
            }
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Credit purchase failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="CREDIT_PURCHASE_FAILED",
                message=f"Failed to purchase credits: {str(e)}"
            )
    
    async def deduct_credits(
        self,
        user_id: UUID,
        amount_cents: int,
        description: str,
        request_id: UUID = None
    ) -> bool:
        """Deduct credits from user account"""
        try:
            # Check current balance
            credits = await self.get_user_credits(user_id)
            current_balance = credits["balance_cents"]
            
            if current_balance < amount_cents:
                return False  # Insufficient credits
            
            # Deduct credits
            await self._add_credits(
                user_id=user_id,
                amount_cents=-amount_cents,
                transaction_type="usage",
                description=description,
                reference_id=str(request_id) if request_id else None
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Credit deduction failed: {str(e)}")
            return False
    
    async def process_ai_request_billing(
        self,
        user_id: UUID,
        request_type: RequestType,
        parameters: Dict[str, Any] = None,
        tokens_used: int = 0,
        processing_time: float = 0,
        request_id: UUID = None
    ) -> Dict[str, Any]:
        """Process billing for AI request"""
        try:
            # Calculate cost
            cost_cents = await self.calculate_request_cost(
                request_type, parameters, tokens_used, processing_time
            )
            
            # Check if user has subscription that covers this request
            subscription_covers = await self._check_subscription_coverage(user_id, request_type)
            
            if subscription_covers:
                # Request covered by subscription
                await self._record_subscription_usage(user_id, request_type, cost_cents, request_id)
                
                return {
                    "billing_method": "subscription",
                    "cost_cents": 0,
                    "covered_by_subscription": True,
                    "subscription_usage_recorded": True
                }
            else:
                # Pay-per-use billing
                credits_deducted = await self.deduct_credits(
                    user_id=user_id,
                    amount_cents=cost_cents,
                    description=f"{request_type.value} request",
                    request_id=request_id
                )
                
                if not credits_deducted:
                    raise CustomException(
                        status_code=402,
                        error_code="INSUFFICIENT_CREDITS",
                        message="Insufficient credits for this request"
                    )
                
                return {
                    "billing_method": "pay_per_use",
                    "cost_cents": cost_cents,
                    "credits_deducted": True,
                    "remaining_balance": (await self.get_user_credits(user_id))["balance_cents"]
                }
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"AI request billing failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="BILLING_PROCESSING_FAILED",
                message=f"Billing processing failed: {str(e)}"
            )
    
    async def generate_usage_report(
        self,
        user_id: UUID,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> Dict[str, Any]:
        """Generate detailed usage report"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=30)
            if not end_date:
                end_date = datetime.utcnow()
            
            # Get usage records
            result = await self.db.execute(
                select(UsageRecord)
                .where(
                    and_(
                        UsageRecord.user_id == user_id,
                        UsageRecord.created_at >= start_date,
                        UsageRecord.created_at <= end_date
                    )
                )
                .order_by(UsageRecord.created_at.desc())
            )
            
            usage_records = result.scalars().all()
            
            # Aggregate statistics
            total_requests = len(usage_records)
            total_cost = sum(record.cost_cents for record in usage_records)
            total_tokens = sum(record.tokens_used for record in usage_records)
            
            # Group by request type
            by_type = {}
            for record in usage_records:
                request_type = record.request_type.value
                if request_type not in by_type:
                    by_type[request_type] = {
                        "count": 0,
                        "cost_cents": 0,
                        "tokens_used": 0
                    }
                
                by_type[request_type]["count"] += 1
                by_type[request_type]["cost_cents"] += record.cost_cents
                by_type[request_type]["tokens_used"] += record.tokens_used
            
            # Group by day
            daily_usage = {}
            for record in usage_records:
                day = record.created_at.date().isoformat()
                if day not in daily_usage:
                    daily_usage[day] = {
                        "requests": 0,
                        "cost_cents": 0,
                        "tokens": 0
                    }
                
                daily_usage[day]["requests"] += 1
                daily_usage[day]["cost_cents"] += record.cost_cents
                daily_usage[day]["tokens"] += record.tokens_used
            
            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                },
                "summary": {
                    "total_requests": total_requests,
                    "total_cost_cents": total_cost,
                    "total_cost_dollars": total_cost / 100,
                    "total_tokens": total_tokens,
                    "average_cost_per_request": total_cost / total_requests if total_requests > 0 else 0
                },
                "by_request_type": by_type,
                "daily_breakdown": daily_usage,
                "recent_requests": [
                    {
                        "id": str(record.id),
                        "request_type": record.request_type.value,
                        "cost_cents": record.cost_cents,
                        "tokens_used": record.tokens_used,
                        "created_at": record.created_at.isoformat(),
                        "metadata": record.metadata
                    }
                    for record in usage_records[:20]  # Last 20 requests
                ]
            }
            
        except Exception as e:
            logger.error(f"Usage report generation failed: {str(e)}")
            return {"error": str(e)}
    
    async def get_billing_summary(self, user_id: UUID) -> Dict[str, Any]:
        """Get comprehensive billing summary"""
        try:
            # Get current credits
            credits = await self.get_user_credits(user_id)
            
            # Get current month usage
            current_month_start = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            current_month_usage = await self.generate_usage_report(user_id, current_month_start)
            
            # Get subscription info
            from app.services.subscription_service import SubscriptionManager
            subscription_manager = SubscriptionManager(self.db)
            subscription = await subscription_manager.get_user_subscription(user_id)
            
            return {
                "credits": credits,
                "current_month_usage": current_month_usage["summary"],
                "subscription": {
                    "active": subscription is not None,
                    "tier": subscription.tier.value if subscription else None,
                    "status": subscription.status.value if subscription else None,
                    "next_billing_date": subscription.current_period_end.isoformat() if subscription and subscription.current_period_end else None
                } if subscription else None,
                "available_credit_packages": [
                    {
                        "name": name,
                        "credits": package["credits"],
                        "bonus_credits": package["bonus"],
                        "total_credits": package["credits"] + package["bonus"],
                        "price_cents": package["price_cents"],
                        "price_dollars": package["price_cents"] / 100
                    }
                    for name, package in self.credit_packages.items()
                ]
            }
            
        except Exception as e:
            logger.error(f"Billing summary generation failed: {str(e)}")
            return {"error": str(e)}
    
    async def _add_credits(
        self,
        user_id: UUID,
        amount_cents: int,
        transaction_type: str,
        description: str,
        reference_id: str = None
    ):
        """Add credit transaction"""
        transaction = CreditTransaction(
            user_id=user_id,
            amount_cents=amount_cents,
            transaction_type=transaction_type,
            description=description,
            reference_id=reference_id,
            created_at=datetime.utcnow()
        )
        
        self.db.add(transaction)
        await self.db.commit()
    
    async def _check_subscription_coverage(self, user_id: UUID, request_type: RequestType) -> bool:
        """Check if user's subscription covers this request type"""
        from app.services.subscription_service import SubscriptionManager
        
        subscription_manager = SubscriptionManager(self.db)
        subscription = await subscription_manager.get_user_subscription(user_id)
        
        if not subscription:
            return False
        
        # Check usage limits
        usage_check = await subscription_manager.check_usage_limits(user_id, request_type)
        return usage_check.get("allowed", False)
    
    async def _record_subscription_usage(
        self,
        user_id: UUID,
        request_type: RequestType,
        cost_cents: int,
        request_id: UUID = None
    ):
        """Record usage for subscription billing"""
        usage_record = UsageRecord(
            user_id=user_id,
            request_type=request_type,
            cost_cents=cost_cents,
            tokens_used=0,  # Will be updated later if available
            metadata={"billing_method": "subscription", "request_id": str(request_id) if request_id else None},
            created_at=datetime.utcnow()
        )
        
        self.db.add(usage_record)
        await self.db.commit()


__all__ = ["BillingService"]
