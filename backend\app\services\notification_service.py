"""
EliteForge AI - Notification Service
Email notifications for billing, subscriptions, and system events
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

import aiosmtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from jinja2 import Environment, FileSystemLoader

from app.core.config import settings

logger = logging.getLogger(__name__)


class NotificationService:
    """Email notification service for user communications"""
    
    def __init__(self):
        # Initialize email templates
        self.template_env = Environment(
            loader=FileSystemLoader("app/templates/emails")
        )
        
        # Email configuration
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.from_email = settings.FROM_EMAIL
        self.from_name = "EliteForge AI"
    
    async def send_subscription_welcome(self, email: str, name: str) -> bool:
        """Send welcome email for new subscription"""
        try:
            template = self.template_env.get_template("subscription_welcome.html")
            html_content = template.render(
                name=name,
                company_name="EliteForge AI",
                support_email="<EMAIL>",
                dashboard_url="https://app.eliteforge.ai/dashboard"
            )
            
            await self._send_email(
                to_email=email,
                subject="Welcome to EliteForge AI - Your Subscription is Active!",
                html_content=html_content
            )
            
            logger.info(f"Sent subscription welcome email to {email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send subscription welcome email: {str(e)}")
            return False
    
    async def send_subscription_cancelled(self, email: str, name: str) -> bool:
        """Send subscription cancellation confirmation"""
        try:
            template = self.template_env.get_template("subscription_cancelled.html")
            html_content = template.render(
                name=name,
                company_name="EliteForge AI",
                support_email="<EMAIL>",
                reactivate_url="https://app.eliteforge.ai/billing"
            )
            
            await self._send_email(
                to_email=email,
                subject="EliteForge AI - Subscription Cancelled",
                html_content=html_content
            )
            
            logger.info(f"Sent subscription cancellation email to {email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send subscription cancellation email: {str(e)}")
            return False
    
    async def send_trial_ending_reminder(self, email: str, name: str, trial_end_date: str) -> bool:
        """Send trial ending reminder"""
        try:
            template = self.template_env.get_template("trial_ending.html")
            html_content = template.render(
                name=name,
                trial_end_date=trial_end_date,
                company_name="EliteForge AI",
                upgrade_url="https://app.eliteforge.ai/billing/upgrade"
            )
            
            await self._send_email(
                to_email=email,
                subject="EliteForge AI - Your Trial Ends Soon",
                html_content=html_content
            )
            
            logger.info(f"Sent trial ending reminder to {email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send trial ending reminder: {str(e)}")
            return False
    
    async def send_payment_confirmation(self, email: str, name: str, amount: float) -> bool:
        """Send payment confirmation"""
        try:
            template = self.template_env.get_template("payment_confirmation.html")
            html_content = template.render(
                name=name,
                amount=f"${amount:.2f}",
                company_name="EliteForge AI",
                invoice_url="https://app.eliteforge.ai/billing/invoices"
            )
            
            await self._send_email(
                to_email=email,
                subject="EliteForge AI - Payment Confirmation",
                html_content=html_content
            )
            
            logger.info(f"Sent payment confirmation to {email} for ${amount:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send payment confirmation: {str(e)}")
            return False
    
    async def send_payment_failed(self, email: str, name: str, failure_reason: str) -> bool:
        """Send payment failure notification"""
        try:
            template = self.template_env.get_template("payment_failed.html")
            html_content = template.render(
                name=name,
                failure_reason=failure_reason,
                company_name="EliteForge AI",
                update_payment_url="https://app.eliteforge.ai/billing/payment-methods"
            )
            
            await self._send_email(
                to_email=email,
                subject="EliteForge AI - Payment Failed",
                html_content=html_content
            )
            
            logger.info(f"Sent payment failure notification to {email}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send payment failure notification: {str(e)}")
            return False
    
    async def send_usage_limit_warning(self, email: str, name: str, usage_percentage: float) -> bool:
        """Send usage limit warning"""
        try:
            template = self.template_env.get_template("usage_limit_warning.html")
            html_content = template.render(
                name=name,
                usage_percentage=f"{usage_percentage:.1f}%",
                company_name="EliteForge AI",
                upgrade_url="https://app.eliteforge.ai/billing/upgrade"
            )
            
            await self._send_email(
                to_email=email,
                subject="EliteForge AI - Usage Limit Warning",
                html_content=html_content
            )
            
            logger.info(f"Sent usage limit warning to {email} ({usage_percentage:.1f}%)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send usage limit warning: {str(e)}")
            return False
    
    async def send_low_credit_warning(self, email: str, name: str, balance: float) -> bool:
        """Send low credit balance warning"""
        try:
            template = self.template_env.get_template("low_credit_warning.html")
            html_content = template.render(
                name=name,
                balance=f"${balance:.2f}",
                company_name="EliteForge AI",
                add_credits_url="https://app.eliteforge.ai/billing/credits"
            )
            
            await self._send_email(
                to_email=email,
                subject="EliteForge AI - Low Credit Balance",
                html_content=html_content
            )
            
            logger.info(f"Sent low credit warning to {email} (${balance:.2f})")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send low credit warning: {str(e)}")
            return False
    
    async def send_invoice_notification(
        self, 
        email: str, 
        name: str, 
        invoice_number: str, 
        amount: float,
        due_date: str
    ) -> bool:
        """Send invoice notification"""
        try:
            template = self.template_env.get_template("invoice_notification.html")
            html_content = template.render(
                name=name,
                invoice_number=invoice_number,
                amount=f"${amount:.2f}",
                due_date=due_date,
                company_name="EliteForge AI",
                pay_invoice_url=f"https://app.eliteforge.ai/billing/invoices/{invoice_number}"
            )
            
            await self._send_email(
                to_email=email,
                subject=f"EliteForge AI - Invoice {invoice_number}",
                html_content=html_content
            )
            
            logger.info(f"Sent invoice notification to {email} for {invoice_number}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send invoice notification: {str(e)}")
            return False
    
    async def send_system_notification(
        self, 
        email: str, 
        name: str, 
        subject: str, 
        message: str
    ) -> bool:
        """Send general system notification"""
        try:
            template = self.template_env.get_template("system_notification.html")
            html_content = template.render(
                name=name,
                message=message,
                company_name="EliteForge AI",
                support_email="<EMAIL>"
            )
            
            await self._send_email(
                to_email=email,
                subject=f"EliteForge AI - {subject}",
                html_content=html_content
            )
            
            logger.info(f"Sent system notification to {email}: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send system notification: {str(e)}")
            return False
    
    async def _send_email(
        self, 
        to_email: str, 
        subject: str, 
        html_content: str,
        text_content: Optional[str] = None
    ) -> bool:
        """Send email using SMTP"""
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email
            
            # Add text content if provided
            if text_content:
                text_part = MIMEText(text_content, "plain")
                message.attach(text_part)
            
            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Send email
            await aiosmtplib.send(
                message,
                hostname=self.smtp_host,
                port=self.smtp_port,
                username=self.smtp_username,
                password=self.smtp_password,
                use_tls=True
            )
            
            return True
            
        except Exception as e:
            logger.error(f"SMTP send failed: {str(e)}")
            return False


__all__ = ["NotificationService"]
