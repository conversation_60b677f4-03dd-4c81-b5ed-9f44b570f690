import React, { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { 
  PhotoIcon, 
  ArrowDownTrayIcon,
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { aiApi } from '../../lib/api'

interface ImageGenerationPanelProps {
  onGenerationStart: () => void
  onGenerationComplete: () => void
  onGenerationError: () => void
  isGenerating: boolean
  subscription?: any
  models: any[]
}

const imageModels = [
  { id: 'stable-diffusion-1.5', name: 'Stable Diffusion 1.5', description: 'Standard image generation' },
  { id: 'stable-diffusion-xl', name: 'Stable Diffusion XL', description: 'High-resolution images (Standard+)' },
  { id: 'flux-1', name: 'FLUX.1', description: 'Advanced image generation (Advanced+)' },
]

const stylePresets = [
  { name: 'Photorealistic', prompt: 'photorealistic, high quality, detailed' },
  { name: 'Digital Art', prompt: 'digital art, concept art, trending on artstation' },
  { name: 'Oil Painting', prompt: 'oil painting, classical art style, masterpiece' },
  { name: 'Anime', prompt: 'anime style, manga, japanese animation' },
  { name: 'Cyberpunk', prompt: 'cyberpunk, neon lights, futuristic, sci-fi' },
  { name: 'Fantasy', prompt: 'fantasy art, magical, ethereal, mystical' },
]

const aspectRatios = [
  { name: '1:1 (Square)', width: 512, height: 512 },
  { name: '4:3 (Landscape)', width: 512, height: 384 },
  { name: '3:4 (Portrait)', width: 384, height: 512 },
  { name: '16:9 (Widescreen)', width: 512, height: 288 },
  { name: '9:16 (Mobile)', width: 288, height: 512 },
]

export default function ImageGenerationPanel({
  onGenerationStart,
  onGenerationComplete,
  onGenerationError,
  isGenerating,
  subscription,
  models
}: ImageGenerationPanelProps) {
  const [prompt, setPrompt] = useState('')
  const [negativePrompt, setNegativePrompt] = useState('')
  const [selectedModel, setSelectedModel] = useState('stable-diffusion-1.5')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [selectedRatio, setSelectedRatio] = useState(0)
  const [parameters, setParameters] = useState({
    num_inference_steps: 20,
    guidance_scale: 7.5,
    scheduler: 'DPMSolverMultistepScheduler',
  })
  const [result, setResult] = useState<any>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)

  const generateMutation = useMutation({
    mutationFn: (data: any) => aiApi.generateImage(data),
    onMutate: () => {
      onGenerationStart()
      setResult(null)
      setImagePreview(null)
    },
    onSuccess: (response) => {
      setResult(response.data)
      if (response.data.result?.image_url) {
        setImagePreview(response.data.result.image_url)
      }
      onGenerationComplete()
    },
    onError: (error) => {
      console.error('Image generation failed:', error)
      onGenerationError()
    },
  })

  const handleGenerate = () => {
    if (!prompt.trim()) return

    const ratio = aspectRatios[selectedRatio]
    generateMutation.mutate({
      prompt: prompt.trim(),
      negative_prompt: negativePrompt.trim() || undefined,
      model: selectedModel,
      width: ratio.width,
      height: ratio.height,
      ...parameters,
    })
  }

  const handleStyleSelect = (style: typeof stylePresets[0]) => {
    const currentPrompt = prompt.trim()
    const newPrompt = currentPrompt 
      ? `${currentPrompt}, ${style.prompt}`
      : style.prompt
    setPrompt(newPrompt)
  }

  const handleDownloadImage = () => {
    if (imagePreview) {
      const link = document.createElement('a')
      link.href = imagePreview
      link.download = 'generated-image.png'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const canUseModel = (modelId: string) => {
    if (!subscription) return false
    const tier = subscription.tier.toLowerCase()
    
    if (modelId.includes('xl') && !['standard', 'advanced', 'enterprise'].includes(tier)) return false
    if (modelId.includes('flux') && !['advanced', 'enterprise'].includes(tier)) return false
    
    return true
  }

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <PhotoIcon className="h-6 w-6 text-purple-500 mr-3" />
        <h3 className="text-lg font-semibold text-gray-900">Image Generation</h3>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          {/* Style Presets */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Style Presets
            </label>
            <div className="grid grid-cols-2 gap-2">
              {stylePresets.map((style) => (
                <button
                  key={style.name}
                  onClick={() => handleStyleSelect(style)}
                  disabled={isGenerating}
                  className="p-2 text-left border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="text-sm font-medium text-gray-900">{style.name}</div>
                  <div className="text-xs text-gray-500 truncate">{style.prompt}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              disabled={isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            >
              {imageModels.map((model) => (
                <option 
                  key={model.id} 
                  value={model.id}
                  disabled={!canUseModel(model.id)}
                >
                  {model.name} - {model.description}
                  {!canUseModel(model.id) ? ' (Upgrade Required)' : ''}
                </option>
              ))}
            </select>
          </div>

          {/* Aspect Ratio */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Aspect Ratio
            </label>
            <div className="grid grid-cols-2 gap-2">
              {aspectRatios.map((ratio, index) => (
                <button
                  key={ratio.name}
                  onClick={() => setSelectedRatio(index)}
                  disabled={isGenerating}
                  className={`p-2 text-left border rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                    selectedRatio === index
                      ? 'border-primary-500 bg-primary-50 text-primary-900'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-sm font-medium">{ratio.name}</div>
                  <div className="text-xs text-gray-500">{ratio.width}×{ratio.height}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Main Prompt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prompt
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              disabled={isGenerating}
              placeholder="Describe the image you want to generate..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
              rows={3}
            />
          </div>

          {/* Negative Prompt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Negative Prompt (Optional)
            </label>
            <textarea
              value={negativePrompt}
              onChange={(e) => setNegativePrompt(e.target.value)}
              disabled={isGenerating}
              placeholder="What you don't want in the image..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
              rows={2}
            />
          </div>

          {/* Advanced Parameters */}
          <div>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-sm text-primary-600 hover:text-primary-700"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-1" />
              Advanced Parameters
            </button>
            
            {showAdvanced && (
              <div className="mt-3 space-y-3 p-3 bg-gray-50 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Inference Steps: {parameters.num_inference_steps}
                  </label>
                  <input
                    type="range"
                    min="10"
                    max="50"
                    value={parameters.num_inference_steps}
                    onChange={(e) => setParameters(prev => ({ ...prev, num_inference_steps: parseInt(e.target.value) }))}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Guidance Scale: {parameters.guidance_scale}
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="20"
                    step="0.5"
                    value={parameters.guidance_scale}
                    onChange={(e) => setParameters(prev => ({ ...prev, guidance_scale: parseFloat(e.target.value) }))}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Scheduler
                  </label>
                  <select
                    value={parameters.scheduler}
                    onChange={(e) => setParameters(prev => ({ ...prev, scheduler: e.target.value }))}
                    disabled={isGenerating}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
                  >
                    <option value="DPMSolverMultistepScheduler">DPM Solver</option>
                    <option value="EulerAncestralDiscreteScheduler">Euler Ancestral</option>
                    <option value="EulerDiscreteScheduler">Euler</option>
                    <option value="LMSDiscreteScheduler">LMS</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Generate Button */}
          <button
            onClick={handleGenerate}
            disabled={!prompt.trim() || isGenerating}
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating...
              </>
            ) : (
              <>
                <PhotoIcon className="h-4 w-4 mr-2" />
                Generate Image
              </>
            )}
          </button>
        </div>

        {/* Result Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">Generated Image</h4>
            {imagePreview && (
              <button
                onClick={handleDownloadImage}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                title="Download image"
              >
                <ArrowDownTrayIcon className="h-4 w-4" />
              </button>
            )}
          </div>

          <div className="border border-gray-200 rounded-lg p-4 min-h-[400px] bg-gray-50">
            {isGenerating ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
                  <p className="text-gray-500">Generating your image...</p>
                </div>
              </div>
            ) : imagePreview ? (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <img
                    src={imagePreview}
                    alt="Generated image"
                    className="max-w-full max-h-96 rounded-lg shadow-lg"
                  />
                </div>
                
                {/* Generation Info */}
                {result && (
                  <div className="pt-4 border-t border-gray-200 text-xs text-gray-500 space-y-1">
                    <div>Processing time: {result.processing_time?.toFixed(2)}s</div>
                    <div>Model: {selectedModel}</div>
                    <div>Size: {aspectRatios[selectedRatio].width}×{aspectRatios[selectedRatio].height}</div>
                    {result.cached && <div>Result was cached</div>}
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400">
                <div className="text-center">
                  <PhotoIcon className="h-12 w-12 mx-auto mb-2" />
                  <p>Generated image will appear here</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
