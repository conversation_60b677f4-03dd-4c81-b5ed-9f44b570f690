"""
EliteForge AI - Redis Client Configuration
Redis connection management for caching, sessions, and rate limiting
"""

import redis.asyncio as redis
import json
import pickle
from typing import Any, Optional, Union
from datetime import timedelta
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# Global Redis client instance
redis_client: Optional[redis.Redis] = None


async def init_redis():
    """Initialize Redis connection"""
    global redis_client
    
    try:
        redis_client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True,
            socket_connect_timeout=5,
            socket_timeout=5,
            retry_on_timeout=True,
            health_check_interval=30
        )
        
        # Test connection
        await redis_client.ping()
        logger.info("Redis connection established successfully")
        
    except Exception as e:
        logger.error(f"Failed to connect to Redis: {str(e)}")
        raise


async def close_redis():
    """Close Redis connection"""
    global redis_client
    if redis_client:
        await redis_client.close()
        logger.info("Redis connection closed")


async def check_redis_health() -> bool:
    """Check Redis health"""
    try:
        if redis_client:
            await redis_client.ping()
            return True
        return False
    except Exception as e:
        logger.error(f"Redis health check failed: {str(e)}")
        return False


class RedisManager:
    """Redis operations manager"""
    
    def __init__(self, client: redis.Redis = None):
        self.client = client or redis_client
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[Union[int, timedelta]] = None,
        serialize: bool = True
    ) -> bool:
        """Set a key-value pair in Redis"""
        try:
            if serialize and not isinstance(value, (str, int, float)):
                value = json.dumps(value, default=str)
            
            if expire:
                if isinstance(expire, timedelta):
                    expire = int(expire.total_seconds())
                return await self.client.setex(key, expire, value)
            else:
                return await self.client.set(key, value)
                
        except Exception as e:
            logger.error(f"Redis SET error for key {key}: {str(e)}")
            return False
    
    async def get(
        self, 
        key: str, 
        deserialize: bool = True,
        default: Any = None
    ) -> Any:
        """Get a value from Redis"""
        try:
            value = await self.client.get(key)
            if value is None:
                return default
            
            if deserialize and isinstance(value, str):
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            
            return value
            
        except Exception as e:
            logger.error(f"Redis GET error for key {key}: {str(e)}")
            return default
    
    async def delete(self, *keys: str) -> int:
        """Delete keys from Redis"""
        try:
            return await self.client.delete(*keys)
        except Exception as e:
            logger.error(f"Redis DELETE error for keys {keys}: {str(e)}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis"""
        try:
            return bool(await self.client.exists(key))
        except Exception as e:
            logger.error(f"Redis EXISTS error for key {key}: {str(e)}")
            return False
    
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiration for a key"""
        try:
            return await self.client.expire(key, seconds)
        except Exception as e:
            logger.error(f"Redis EXPIRE error for key {key}: {str(e)}")
            return False
    
    async def ttl(self, key: str) -> int:
        """Get time to live for a key"""
        try:
            return await self.client.ttl(key)
        except Exception as e:
            logger.error(f"Redis TTL error for key {key}: {str(e)}")
            return -1
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """Increment a key's value"""
        try:
            return await self.client.incr(key, amount)
        except Exception as e:
            logger.error(f"Redis INCR error for key {key}: {str(e)}")
            return 0
    
    async def decr(self, key: str, amount: int = 1) -> int:
        """Decrement a key's value"""
        try:
            return await self.client.decr(key, amount)
        except Exception as e:
            logger.error(f"Redis DECR error for key {key}: {str(e)}")
            return 0
    
    async def hset(self, name: str, mapping: dict) -> int:
        """Set hash fields"""
        try:
            # Serialize complex values
            serialized_mapping = {}
            for key, value in mapping.items():
                if isinstance(value, (dict, list)):
                    serialized_mapping[key] = json.dumps(value, default=str)
                else:
                    serialized_mapping[key] = value
            
            return await self.client.hset(name, mapping=serialized_mapping)
        except Exception as e:
            logger.error(f"Redis HSET error for hash {name}: {str(e)}")
            return 0
    
    async def hget(self, name: str, key: str, deserialize: bool = True) -> Any:
        """Get hash field value"""
        try:
            value = await self.client.hget(name, key)
            if value is None:
                return None
            
            if deserialize and isinstance(value, str):
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            
            return value
        except Exception as e:
            logger.error(f"Redis HGET error for hash {name}, key {key}: {str(e)}")
            return None
    
    async def hgetall(self, name: str, deserialize: bool = True) -> dict:
        """Get all hash fields"""
        try:
            data = await self.client.hgetall(name)
            if not data:
                return {}
            
            if deserialize:
                deserialized_data = {}
                for key, value in data.items():
                    if isinstance(value, str):
                        try:
                            deserialized_data[key] = json.loads(value)
                        except (json.JSONDecodeError, TypeError):
                            deserialized_data[key] = value
                    else:
                        deserialized_data[key] = value
                return deserialized_data
            
            return data
        except Exception as e:
            logger.error(f"Redis HGETALL error for hash {name}: {str(e)}")
            return {}
    
    async def hdel(self, name: str, *keys: str) -> int:
        """Delete hash fields"""
        try:
            return await self.client.hdel(name, *keys)
        except Exception as e:
            logger.error(f"Redis HDEL error for hash {name}, keys {keys}: {str(e)}")
            return 0
    
    async def lpush(self, name: str, *values: Any) -> int:
        """Push values to the left of a list"""
        try:
            serialized_values = []
            for value in values:
                if isinstance(value, (dict, list)):
                    serialized_values.append(json.dumps(value, default=str))
                else:
                    serialized_values.append(str(value))
            
            return await self.client.lpush(name, *serialized_values)
        except Exception as e:
            logger.error(f"Redis LPUSH error for list {name}: {str(e)}")
            return 0
    
    async def rpop(self, name: str, deserialize: bool = True) -> Any:
        """Pop value from the right of a list"""
        try:
            value = await self.client.rpop(name)
            if value is None:
                return None
            
            if deserialize and isinstance(value, str):
                try:
                    return json.loads(value)
                except (json.JSONDecodeError, TypeError):
                    return value
            
            return value
        except Exception as e:
            logger.error(f"Redis RPOP error for list {name}: {str(e)}")
            return None
    
    async def llen(self, name: str) -> int:
        """Get list length"""
        try:
            return await self.client.llen(name)
        except Exception as e:
            logger.error(f"Redis LLEN error for list {name}: {str(e)}")
            return 0


class CacheManager:
    """High-level cache management"""
    
    def __init__(self, redis_manager: RedisManager = None):
        self.redis = redis_manager or RedisManager()
    
    async def cache_user_session(self, user_id: str, session_data: dict, expire_hours: int = 24):
        """Cache user session data"""
        key = f"session:{user_id}"
        expire = timedelta(hours=expire_hours)
        return await self.redis.set(key, session_data, expire=expire)
    
    async def get_user_session(self, user_id: str) -> Optional[dict]:
        """Get user session data"""
        key = f"session:{user_id}"
        return await self.redis.get(key)
    
    async def invalidate_user_session(self, user_id: str):
        """Invalidate user session"""
        key = f"session:{user_id}"
        return await self.redis.delete(key)
    
    async def cache_api_response(self, endpoint: str, params: dict, response: dict, expire_minutes: int = 15):
        """Cache API response"""
        cache_key = f"api:{endpoint}:{hash(str(sorted(params.items())))}"
        expire = timedelta(minutes=expire_minutes)
        return await self.redis.set(cache_key, response, expire=expire)
    
    async def get_cached_api_response(self, endpoint: str, params: dict) -> Optional[dict]:
        """Get cached API response"""
        cache_key = f"api:{endpoint}:{hash(str(sorted(params.items())))}"
        return await self.redis.get(cache_key)
    
    async def cache_model_output(self, model_name: str, input_hash: str, output: Any, expire_hours: int = 1):
        """Cache AI model output"""
        key = f"model:{model_name}:{input_hash}"
        expire = timedelta(hours=expire_hours)
        return await self.redis.set(key, output, expire=expire)
    
    async def get_cached_model_output(self, model_name: str, input_hash: str) -> Any:
        """Get cached AI model output"""
        key = f"model:{model_name}:{input_hash}"
        return await self.redis.get(key)


class RateLimiter:
    """Redis-based rate limiter"""
    
    def __init__(self, redis_manager: RedisManager = None):
        self.redis = redis_manager or RedisManager()
    
    async def is_allowed(
        self, 
        identifier: str, 
        limit: int, 
        window_seconds: int,
        namespace: str = "rate_limit"
    ) -> tuple[bool, dict]:
        """Check if request is allowed under rate limit"""
        key = f"{namespace}:{identifier}"
        
        try:
            current = await self.redis.get(key, deserialize=False)
            
            if current is None:
                # First request
                await self.redis.set(key, 1, expire=window_seconds, serialize=False)
                return True, {
                    "allowed": True,
                    "count": 1,
                    "limit": limit,
                    "remaining": limit - 1,
                    "reset_time": window_seconds
                }
            
            current_count = int(current)
            
            if current_count >= limit:
                ttl = await self.redis.ttl(key)
                return False, {
                    "allowed": False,
                    "count": current_count,
                    "limit": limit,
                    "remaining": 0,
                    "reset_time": ttl
                }
            
            # Increment counter
            new_count = await self.redis.incr(key)
            ttl = await self.redis.ttl(key)
            
            return True, {
                "allowed": True,
                "count": new_count,
                "limit": limit,
                "remaining": limit - new_count,
                "reset_time": ttl
            }
            
        except Exception as e:
            logger.error(f"Rate limiter error: {str(e)}")
            # Fail open - allow request if Redis is down
            return True, {
                "allowed": True,
                "count": 0,
                "limit": limit,
                "remaining": limit,
                "reset_time": window_seconds,
                "error": "Rate limiter unavailable"
            }


# Create global instances
redis_manager = RedisManager()
cache_manager = CacheManager(redis_manager)
rate_limiter = RateLimiter(redis_manager)

# Export commonly used items
__all__ = [
    "init_redis",
    "close_redis",
    "check_redis_health",
    "redis_client",
    "redis_manager",
    "cache_manager",
    "rate_limiter",
    "RedisManager",
    "CacheManager",
    "RateLimiter"
]
