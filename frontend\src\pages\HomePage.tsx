import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { motion } from 'framer-motion';
import { 
  SparklesIcon, 
  RocketLaunchIcon, 
  CpuChipIcon,
  LightBulbIcon,
  ShieldCheckIcon,
  BoltIcon
} from '@heroicons/react/24/outline';

// Components
import Hero from '../components/home/<USER>';
import Features from '../components/home/<USER>';
import Pricing from '../components/home/<USER>';
import Testimonials from '../components/home/<USER>';
import CTA from '../components/home/<USER>';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: <SparklesIcon className="h-8 w-8" />,
      title: 'Advanced AI Models',
      description: 'Access cutting-edge AI models including Stable Diffusion, FLUX.1, Llama, and custom algorithms.',
    },
    {
      icon: <RocketLaunchIcon className="h-8 w-8" />,
      title: 'Lightning Fast',
      description: 'Optimized infrastructure ensures rapid processing and minimal wait times for all requests.',
    },
    {
      icon: <CpuChipIcon className="h-8 w-8" />,
      title: 'Scalable Architecture',
      description: 'Built on modern cloud-native architecture that scales with your needs automatically.',
    },
    {
      icon: <LightBulbIcon className="h-8 w-8" />,
      title: 'Intelligent Workflows',
      description: 'Smart automation and workflow optimization to maximize your productivity.',
    },
    {
      icon: <ShieldCheckIcon className="h-8 w-8" />,
      title: 'Enterprise Security',
      description: 'Bank-level security with encryption, compliance, and audit trails built-in.',
    },
    {
      icon: <BoltIcon className="h-8 w-8" />,
      title: 'API-First Design',
      description: 'Comprehensive REST API with SDKs for seamless integration into your applications.',
    },
  ];

  const stats = [
    { label: 'Active Users', value: '10,000+' },
    { label: 'AI Requests Processed', value: '1M+' },
    { label: 'Uptime', value: '99.9%' },
    { label: 'Countries Served', value: '50+' },
  ];

  return (
    <>
      <Helmet>
        <title>EliteForge AI - Premium AI Platform for Professionals</title>
        <meta 
          name="description" 
          content="Transform your workflow with EliteForge AI's premium AI platform. Access advanced models, enterprise security, and scalable infrastructure." 
        />
        <meta name="keywords" content="AI platform, artificial intelligence, machine learning, enterprise AI, API" />
        <meta property="og:title" content="EliteForge AI - Premium AI Platform" />
        <meta property="og:description" content="Transform your workflow with premium AI capabilities" />
        <meta property="og:type" content="website" />
        <link rel="canonical" href="https://eliteforge-ai.com" />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Hero Section */}
        <Hero />

        {/* Stats Section */}
        <section className="py-16 bg-white/5 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-300 text-sm md:text-base">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Powerful Features for
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
                  {' '}Modern Teams
                </span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Everything you need to harness the power of AI in your workflow, 
                from simple automation to complex enterprise solutions.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-300"
                >
                  <div className="text-purple-400 mb-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Pricing Preview */}
        <section className="py-20 bg-white/5 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Simple, Transparent
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">
                  {' '}Pricing
                </span>
              </h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
                Choose the plan that fits your needs. Start free and scale as you grow.
              </p>
              <Link
                to="/pricing"
                className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transition-all duration-300"
              >
                View All Plans
              </Link>
            </motion.div>

            {/* Quick pricing overview */}
            <div className="grid md:grid-cols-4 gap-6">
              {[
                { name: 'Free', price: '$0', requests: '100/month' },
                { name: 'Basic', price: '$9.99', requests: '1,000/month' },
                { name: 'Pro', price: '$29.99', requests: '10,000/month' },
                { name: 'Enterprise', price: '$99.99', requests: 'Unlimited' },
              ].map((plan, index) => (
                <motion.div
                  key={plan.name}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center"
                >
                  <h3 className="text-lg font-semibold text-white mb-2">
                    {plan.name}
                  </h3>
                  <div className="text-2xl font-bold text-purple-400 mb-2">
                    {plan.price}
                  </div>
                  <div className="text-gray-300 text-sm">
                    {plan.requests}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Ready to Transform Your
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-400">
                  {' '}Workflow?
                </span>
              </h2>
              <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
                Join thousands of professionals who are already using EliteForge AI 
                to accelerate their work and unlock new possibilities.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/auth/register"
                  className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  Start Free Trial
                </Link>
                <Link
                  to="/contact"
                  className="inline-flex items-center px-8 py-4 border border-white/30 text-lg font-medium rounded-md text-white hover:bg-white/10 transition-all duration-300"
                >
                  Contact Sales
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </div>
    </>
  );
};

export default HomePage;
