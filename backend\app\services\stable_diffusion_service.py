"""
EliteForge AI - Stable Diffusion Service
Specialized service for Stable Diffusion image generation with GPU optimization
"""

import asyncio
import io
import logging
import time
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID
import base64

import torch
import numpy as np
from PIL import Image
from diffusers import (
    StableDiffusionPipeline,
    StableDiffusionImg2ImgPipeline,
    StableDiffusionInpaintPipeline,
    DPMSolverMultistepScheduler,
    EulerAncestralDiscreteScheduler,
    DDIMScheduler
)
from diffusers.utils import make_image_grid

from app.core.config import settings
from app.core.exceptions import CustomException
from app.utils.file_storage import FileStorageService
from app.utils.image_processing import preprocess_image, postprocess_image, add_watermark

logger = logging.getLogger(__name__)


class StableDiffusionConfig:
    """Configuration for Stable Diffusion models"""
    
    MODELS = {
        "sd-1.5": {
            "model_id": "runwayml/stable-diffusion-v1-5",
            "description": "Standard Stable Diffusion v1.5",
            "tier_required": "basic",
            "memory_usage": 4096,
        },
        "sd-2.1": {
            "model_id": "stabilityai/stable-diffusion-2-1",
            "description": "Stable Diffusion v2.1 with improved quality",
            "tier_required": "standard",
            "memory_usage": 5120,
        },
        "sd-xl": {
            "model_id": "stabilityai/stable-diffusion-xl-base-1.0",
            "description": "Stable Diffusion XL for high-resolution images",
            "tier_required": "advanced",
            "memory_usage": 8192,
        },
        "dreamshaper": {
            "model_id": "Lykon/DreamShaper",
            "description": "DreamShaper - Enhanced artistic model",
            "tier_required": "standard",
            "memory_usage": 4096,
        },
        "realistic-vision": {
            "model_id": "SG161222/Realistic_Vision_V4.0",
            "description": "Realistic Vision - Photorealistic model",
            "tier_required": "advanced",
            "memory_usage": 4096,
        }
    }
    
    SCHEDULERS = {
        "dpm": DPMSolverMultistepScheduler,
        "euler_a": EulerAncestralDiscreteScheduler,
        "ddim": DDIMScheduler,
    }
    
    DEFAULT_PARAMS = {
        "width": 512,
        "height": 512,
        "num_inference_steps": 20,
        "guidance_scale": 7.5,
        "num_images_per_prompt": 1,
        "scheduler": "dpm",
        "safety_checker": True,
    }


class StableDiffusionService:
    """Stable Diffusion image generation service"""
    
    def __init__(self):
        self.device = self._get_optimal_device()
        self.loaded_pipelines = {}
        self.file_storage = FileStorageService()
        self.config = StableDiffusionConfig()
        
        # Performance optimizations
        self.enable_attention_slicing = True
        self.enable_memory_efficient_attention = True
        self.enable_cpu_offload = False
        
        logger.info(f"StableDiffusionService initialized on device: {self.device}")
    
    def _get_optimal_device(self) -> str:
        """Determine optimal device for inference"""
        if torch.cuda.is_available():
            device = f"cuda:{torch.cuda.current_device()}"
            logger.info(f"Using CUDA device: {device}")
            return device
        elif torch.backends.mps.is_available():
            logger.info("Using MPS device")
            return "mps"
        else:
            logger.info("Using CPU device")
            return "cpu"
    
    async def load_pipeline(
        self, 
        model_name: str = "sd-1.5", 
        pipeline_type: str = "txt2img"
    ) -> Any:
        """Load Stable Diffusion pipeline with caching"""
        
        cache_key = f"{model_name}_{pipeline_type}"
        
        if cache_key in self.loaded_pipelines:
            logger.info(f"Using cached pipeline: {cache_key}")
            return self.loaded_pipelines[cache_key]
        
        model_config = self.config.MODELS.get(model_name)
        if not model_config:
            raise CustomException(
                status_code=400,
                error_code="MODEL_NOT_FOUND",
                message=f"Model {model_name} not found"
            )
        
        try:
            logger.info(f"Loading pipeline: {cache_key}")
            start_time = time.time()
            
            model_id = model_config["model_id"]
            
            # Select pipeline class based on type
            if pipeline_type == "txt2img":
                pipeline_class = StableDiffusionPipeline
            elif pipeline_type == "img2img":
                pipeline_class = StableDiffusionImg2ImgPipeline
            elif pipeline_type == "inpaint":
                pipeline_class = StableDiffusionInpaintPipeline
            else:
                raise ValueError(f"Unknown pipeline type: {pipeline_type}")
            
            # Load pipeline
            pipeline = pipeline_class.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if self.device.startswith("cuda") else torch.float32,
                use_safetensors=True,
                cache_dir=settings.MODEL_CACHE_DIR,
                safety_checker=None if not self.config.DEFAULT_PARAMS["safety_checker"] else None,
                requires_safety_checker=self.config.DEFAULT_PARAMS["safety_checker"]
            )
            
            # Move to device
            pipeline = pipeline.to(self.device)
            
            # Apply optimizations
            await self._optimize_pipeline(pipeline)
            
            load_time = time.time() - start_time
            logger.info(f"Pipeline {cache_key} loaded in {load_time:.2f}s")
            
            self.loaded_pipelines[cache_key] = pipeline
            return pipeline
            
        except Exception as e:
            logger.error(f"Failed to load pipeline {cache_key}: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="PIPELINE_LOAD_FAILED",
                message=f"Failed to load pipeline: {str(e)}"
            )
    
    async def _optimize_pipeline(self, pipeline: Any):
        """Apply performance optimizations to pipeline"""
        try:
            # Enable attention slicing for memory efficiency
            if self.enable_attention_slicing and hasattr(pipeline, "enable_attention_slicing"):
                pipeline.enable_attention_slicing()
                logger.debug("Enabled attention slicing")
            
            # Enable memory efficient attention (xformers)
            if self.enable_memory_efficient_attention:
                try:
                    if hasattr(pipeline, "enable_xformers_memory_efficient_attention"):
                        pipeline.enable_xformers_memory_efficient_attention()
                        logger.debug("Enabled xformers memory efficient attention")
                except Exception as e:
                    logger.warning(f"Could not enable xformers: {str(e)}")
            
            # Enable CPU offload for large models
            if self.enable_cpu_offload and hasattr(pipeline, "enable_model_cpu_offload"):
                pipeline.enable_model_cpu_offload()
                logger.debug("Enabled CPU offload")
            
            # Compile model for faster inference (PyTorch 2.0+)
            if hasattr(torch, "compile") and self.device.startswith("cuda"):
                try:
                    pipeline.unet = torch.compile(pipeline.unet, mode="reduce-overhead", fullgraph=True)
                    logger.debug("Compiled UNet model")
                except Exception as e:
                    logger.warning(f"Could not compile model: {str(e)}")
                    
        except Exception as e:
            logger.warning(f"Pipeline optimization failed: {str(e)}")
    
    async def generate_image(
        self,
        prompt: str,
        negative_prompt: str = None,
        model_name: str = "sd-1.5",
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate image from text prompt"""
        
        # Merge parameters with defaults
        params = {**self.config.DEFAULT_PARAMS, **(parameters or {})}
        
        # Validate parameters
        params = self._validate_parameters(params)
        
        # Load pipeline
        pipeline = await self.load_pipeline(model_name, "txt2img")
        
        # Set scheduler if specified
        if params.get("scheduler") and params["scheduler"] in self.config.SCHEDULERS:
            scheduler_class = self.config.SCHEDULERS[params["scheduler"]]
            pipeline.scheduler = scheduler_class.from_config(pipeline.scheduler.config)
        
        try:
            logger.info(f"Generating image with prompt: {prompt[:100]}...")
            start_time = time.time()
            
            # Prepare generation parameters
            generation_params = {
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "width": params["width"],
                "height": params["height"],
                "num_inference_steps": params["num_inference_steps"],
                "guidance_scale": params["guidance_scale"],
                "num_images_per_prompt": params["num_images_per_prompt"],
                "generator": torch.Generator(device=self.device).manual_seed(params.get("seed", -1)) if params.get("seed", -1) >= 0 else None,
            }
            
            # Generate images
            with torch.inference_mode():
                result = pipeline(**generation_params)
            
            generation_time = time.time() - start_time
            
            # Process and save images
            image_urls = []
            for i, image in enumerate(result.images):
                # Post-process image
                processed_image = await postprocess_image(image, params)
                
                # Save image
                image_url = await self.file_storage.save_generated_image(
                    processed_image, 
                    f"sd_{model_name}_{int(time.time())}_{i}.png"
                )
                image_urls.append(image_url)
            
            logger.info(f"Generated {len(image_urls)} images in {generation_time:.2f}s")
            
            return {
                "images": image_urls,
                "parameters": params,
                "model_name": model_name,
                "generation_time": generation_time,
                "prompt": prompt,
                "negative_prompt": negative_prompt,
                "nsfw_detected": getattr(result, "nsfw_content_detected", [False] * len(result.images))
            }
            
        except Exception as e:
            logger.error(f"Image generation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="IMAGE_GENERATION_FAILED",
                message=f"Image generation failed: {str(e)}"
            )
    
    async def image_to_image(
        self,
        prompt: str,
        init_image: Image.Image,
        strength: float = 0.8,
        model_name: str = "sd-1.5",
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate image from image and prompt"""
        
        params = {**self.config.DEFAULT_PARAMS, **(parameters or {})}
        params = self._validate_parameters(params)
        
        # Load img2img pipeline
        pipeline = await self.load_pipeline(model_name, "img2img")
        
        try:
            logger.info(f"Generating img2img with prompt: {prompt[:100]}...")
            start_time = time.time()
            
            # Preprocess input image
            init_image = await preprocess_image(init_image, params["width"], params["height"])
            
            generation_params = {
                "prompt": prompt,
                "image": init_image,
                "strength": strength,
                "num_inference_steps": params["num_inference_steps"],
                "guidance_scale": params["guidance_scale"],
                "num_images_per_prompt": params["num_images_per_prompt"],
            }
            
            with torch.inference_mode():
                result = pipeline(**generation_params)
            
            generation_time = time.time() - start_time
            
            # Save generated images
            image_urls = []
            for i, image in enumerate(result.images):
                processed_image = await postprocess_image(image, params)
                image_url = await self.file_storage.save_generated_image(
                    processed_image,
                    f"img2img_{model_name}_{int(time.time())}_{i}.png"
                )
                image_urls.append(image_url)
            
            return {
                "images": image_urls,
                "parameters": params,
                "model_name": model_name,
                "generation_time": generation_time,
                "strength": strength
            }
            
        except Exception as e:
            logger.error(f"Img2img generation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="IMG2IMG_GENERATION_FAILED",
                message=f"Img2img generation failed: {str(e)}"
            )
    
    def _validate_parameters(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize generation parameters"""
        validated = params.copy()
        
        # Validate dimensions
        validated["width"] = max(64, min(1024, validated.get("width", 512)))
        validated["height"] = max(64, min(1024, validated.get("height", 512)))
        
        # Ensure dimensions are multiples of 8
        validated["width"] = (validated["width"] // 8) * 8
        validated["height"] = (validated["height"] // 8) * 8
        
        # Validate inference steps
        validated["num_inference_steps"] = max(1, min(100, validated.get("num_inference_steps", 20)))
        
        # Validate guidance scale
        validated["guidance_scale"] = max(1.0, min(20.0, validated.get("guidance_scale", 7.5)))
        
        # Validate number of images
        validated["num_images_per_prompt"] = max(1, min(4, validated.get("num_images_per_prompt", 1)))
        
        # Validate seed
        if "seed" in validated and validated["seed"] is not None:
            validated["seed"] = max(-1, min(2**32 - 1, int(validated["seed"])))
        
        return validated
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available Stable Diffusion models"""
        models = []
        for name, config in self.config.MODELS.items():
            models.append({
                "name": name,
                "model_id": config["model_id"],
                "description": config["description"],
                "tier_required": config["tier_required"],
                "memory_usage_mb": config["memory_usage"]
            })
        return models
    
    async def get_memory_usage(self) -> Dict[str, float]:
        """Get current memory usage statistics"""
        stats = {}
        
        if torch.cuda.is_available():
            stats["gpu_allocated_mb"] = torch.cuda.memory_allocated() / (1024 * 1024)
            stats["gpu_reserved_mb"] = torch.cuda.memory_reserved() / (1024 * 1024)
            stats["gpu_total_mb"] = torch.cuda.get_device_properties(0).total_memory / (1024 * 1024)
        
        stats["loaded_pipelines"] = len(self.loaded_pipelines)
        stats["pipeline_names"] = list(self.loaded_pipelines.keys())
        
        return stats
    
    async def clear_cache(self):
        """Clear loaded pipelines and free memory"""
        self.loaded_pipelines.clear()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        import gc
        gc.collect()
        
        logger.info("Cleared Stable Diffusion cache")


# Global service instance
stable_diffusion_service = StableDiffusionService()

__all__ = ["StableDiffusionService", "stable_diffusion_service", "StableDiffusionConfig"]
