"""
EliteForge AI - Authentication Endpoints
JWT-based authentication with role-based access control
"""

from datetime import timed<PERSON><PERSON>
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordR<PERSON><PERSON>Form
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import create_access_token, create_refresh_token, verify_token
from app.core.config import settings
from app.services.user_service import UserService
from app.services.auth_service import AuthService
from app.models.auth import (
    Token,
    UserLogin,
    UserRegister,
    UserResponse,
    PasswordReset,
    PasswordResetConfirm,
    RefreshTokenRequest
)
from app.models.user import UserCreate
from app.utils.email import send_verification_email, send_password_reset_email
from app.core.exceptions import CustomException
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Register a new user"""
    try:
        user_service = UserService(db)
        auth_service = AuthService(db)
        
        # Check if user already exists
        existing_user = await user_service.get_user_by_email(user_data.email)
        if existing_user:
            raise CustomException(
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="USER_ALREADY_EXISTS",
                message="User with this email already exists"
            )
        
        existing_username = await user_service.get_user_by_username(user_data.username)
        if existing_username:
            raise CustomException(
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="USERNAME_TAKEN",
                message="Username is already taken"
            )
        
        # Create user
        user_create = UserCreate(
            email=user_data.email,
            username=user_data.username,
            password=user_data.password,
            full_name=user_data.full_name
        )
        
        user = await user_service.create_user(user_create)
        
        # Send verification email
        try:
            verification_token = await auth_service.create_verification_token(user.id)
            await send_verification_email(user.email, user.full_name or user.username, verification_token)
        except Exception as e:
            logger.error(f"Failed to send verification email: {str(e)}")
            # Don't fail registration if email fails
        
        # Log registration
        logger.info(f"New user registered: {user.email}")
        
        return UserResponse.from_orm(user)
        
    except CustomException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {str(e)}")
        raise CustomException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="REGISTRATION_FAILED",
            message="Failed to register user"
        )


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    request: Request = None,
    db: AsyncSession = Depends(get_db)
):
    """Login user and return JWT tokens"""
    try:
        auth_service = AuthService(db)
        
        # Authenticate user
        user = await auth_service.authenticate_user(form_data.username, form_data.password)
        if not user:
            raise CustomException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code="INVALID_CREDENTIALS",
                message="Invalid email or password"
            )
        
        # Check if user is active
        if not user.is_active:
            raise CustomException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code="ACCOUNT_DISABLED",
                message="Account is disabled"
            )
        
        # Create tokens
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        refresh_token_expires = timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
        
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email, "role": user.role.value},
            expires_delta=access_token_expires
        )
        
        refresh_token = create_refresh_token(
            data={"sub": str(user.id)},
            expires_delta=refresh_token_expires
        )
        
        # Update last login
        await auth_service.update_last_login(user.id)
        
        # Log successful login
        logger.info(f"User logged in: {user.email}")
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except CustomException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise CustomException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="LOGIN_FAILED",
            message="Login failed"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        # Verify refresh token
        payload = verify_token(refresh_data.refresh_token)
        user_id = payload.get("sub")
        
        if not user_id:
            raise CustomException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code="INVALID_TOKEN",
                message="Invalid refresh token"
            )
        
        # Get user
        user_service = UserService(db)
        user = await user_service.get_user_by_id(user_id)
        
        if not user or not user.is_active:
            raise CustomException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code="USER_NOT_FOUND",
                message="User not found or inactive"
            )
        
        # Create new access token
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id), "email": user.email, "role": user.role.value},
            expires_delta=access_token_expires
        )
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_data.refresh_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        )
        
    except CustomException:
        raise
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}")
        raise CustomException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code="TOKEN_REFRESH_FAILED",
            message="Failed to refresh token"
        )


@router.post("/verify-email")
async def verify_email(
    token: str,
    db: AsyncSession = Depends(get_db)
):
    """Verify user email address"""
    try:
        auth_service = AuthService(db)
        
        # Verify token and get user
        user = await auth_service.verify_email_token(token)
        
        if not user:
            raise CustomException(
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="INVALID_TOKEN",
                message="Invalid or expired verification token"
            )
        
        logger.info(f"Email verified for user: {user.email}")
        
        return {"message": "Email verified successfully"}
        
    except CustomException:
        raise
    except Exception as e:
        logger.error(f"Email verification error: {str(e)}")
        raise CustomException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="VERIFICATION_FAILED",
            message="Email verification failed"
        )


@router.post("/forgot-password")
async def forgot_password(
    password_reset: PasswordReset,
    db: AsyncSession = Depends(get_db)
):
    """Send password reset email"""
    try:
        user_service = UserService(db)
        auth_service = AuthService(db)
        
        # Get user by email
        user = await user_service.get_user_by_email(password_reset.email)
        
        if user:
            # Create reset token
            reset_token = await auth_service.create_password_reset_token(user.id)
            
            # Send reset email
            try:
                await send_password_reset_email(user.email, user.full_name or user.username, reset_token)
            except Exception as e:
                logger.error(f"Failed to send password reset email: {str(e)}")
                raise CustomException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code="EMAIL_SEND_FAILED",
                    message="Failed to send password reset email"
                )
        
        # Always return success to prevent email enumeration
        return {"message": "If the email exists, a password reset link has been sent"}
        
    except CustomException:
        raise
    except Exception as e:
        logger.error(f"Password reset request error: {str(e)}")
        raise CustomException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="PASSWORD_RESET_FAILED",
            message="Password reset request failed"
        )


@router.post("/reset-password")
async def reset_password(
    password_reset_confirm: PasswordResetConfirm,
    db: AsyncSession = Depends(get_db)
):
    """Reset password using reset token"""
    try:
        auth_service = AuthService(db)
        
        # Reset password
        success = await auth_service.reset_password(
            password_reset_confirm.token,
            password_reset_confirm.new_password
        )
        
        if not success:
            raise CustomException(
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code="INVALID_TOKEN",
                message="Invalid or expired reset token"
            )
        
        return {"message": "Password reset successfully"}
        
    except CustomException:
        raise
    except Exception as e:
        logger.error(f"Password reset error: {str(e)}")
        raise CustomException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code="PASSWORD_RESET_FAILED",
            message="Password reset failed"
        )


@router.post("/logout")
async def logout(
    refresh_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """Logout user and invalidate refresh token"""
    try:
        auth_service = AuthService(db)
        
        # Invalidate refresh token
        await auth_service.invalidate_refresh_token(refresh_data.refresh_token)
        
        return {"message": "Logged out successfully"}
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        # Don't fail logout even if token invalidation fails
        return {"message": "Logged out successfully"}
