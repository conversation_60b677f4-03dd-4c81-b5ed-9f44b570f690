"""
EliteForge AI - Payoneer Payment Service
Integration with Payoneer payment processing for subscription management
"""

import asyncio
import hashlib
import hmac
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

import httpx
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.exceptions import CustomException
from app.db.models import Payment, Subscription, SubscriptionStatus, SubscriptionTier, User
from app.services.user_service import UserService

logger = logging.getLogger(__name__)


class PayoneerConfig:
    """Payoneer API configuration"""
    
    # Payoneer API endpoints
    BASE_URL = "https://api.payoneer.com/v4"
    SANDBOX_URL = "https://api.sandbox.payoneer.com/v4"
    
    # Subscription pricing (in cents)
    SUBSCRIPTION_PRICES = {
        SubscriptionTier.FREE: 0,
        SubscriptionTier.BASIC: 999,  # $9.99
        SubscriptionTier.PRO: 2999,   # $29.99
        SubscriptionTier.ENTERPRISE: 9999,  # $99.99
    }
    
    # Pay-per-use pricing (in cents)
    USAGE_PRICES = {
        "text_generation": 1,    # $0.01
        "image_generation": 5,   # $0.05
        "code_generation": 3,    # $0.03
        "custom_algorithm": 10,  # $0.10
    }
    
    # Request limits per tier
    TIER_LIMITS = {
        SubscriptionTier.FREE: 100,
        SubscriptionTier.BASIC: 1000,
        SubscriptionTier.PRO: 10000,
        SubscriptionTier.ENTERPRISE: -1,  # Unlimited
    }


class PayoneerService:
    """Payoneer payment processing service"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.config = PayoneerConfig()
        self.user_service = UserService(db)
        
        # Payoneer API credentials
        self.api_key = settings.PAYONEER_API_KEY
        self.api_secret = settings.PAYONEER_API_SECRET
        self.webhook_secret = settings.PAYONEER_WEBHOOK_SECRET
        
        # Use sandbox for development
        self.base_url = self.config.SANDBOX_URL if settings.ENVIRONMENT != "production" else self.config.BASE_URL
        
        # HTTP client for API calls
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "User-Agent": "EliteForge-AI/1.0"
            },
            timeout=30.0
        )
        
        logger.info(f"PayoneerService initialized with base URL: {self.base_url}")
    
    async def create_customer(self, user: User) -> Dict[str, Any]:
        """Create a Payoneer customer"""
        try:
            customer_data = {
                "external_id": str(user.id),
                "email": user.email,
                "first_name": user.full_name.split()[0] if user.full_name else user.username,
                "last_name": " ".join(user.full_name.split()[1:]) if user.full_name and len(user.full_name.split()) > 1 else "",
                "phone": getattr(user, 'phone', ''),
                "address": {
                    "country": "US",  # Default country
                    "state": "",
                    "city": "",
                    "zip": "",
                    "line1": ""
                },
                "metadata": {
                    "user_id": str(user.id),
                    "created_at": datetime.utcnow().isoformat(),
                    "platform": "EliteForge AI"
                }
            }
            
            response = await self.client.post("/customers", json=customer_data)
            response.raise_for_status()
            
            customer = response.json()
            logger.info(f"Created Payoneer customer: {customer['id']} for user: {user.id}")
            
            return customer
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Failed to create Payoneer customer: {e.response.text}")
            raise CustomException(
                status_code=500,
                error_code="CUSTOMER_CREATION_FAILED",
                message="Failed to create payment customer"
            )
        except Exception as e:
            logger.error(f"Customer creation error: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="CUSTOMER_CREATION_ERROR",
                message=f"Customer creation failed: {str(e)}"
            )
    
    async def create_subscription(
        self,
        user_id: UUID,
        tier: SubscriptionTier,
        payment_method_id: str = None
    ) -> Dict[str, Any]:
        """Create a subscription for a user"""
        try:
            user = await self.user_service.get_user_by_id(user_id)
            if not user:
                raise CustomException(
                    status_code=404,
                    error_code="USER_NOT_FOUND",
                    message="User not found"
                )
            
            # Get or create Payoneer customer
            customer = await self._get_or_create_customer(user)
            
            # Create subscription data
            price_cents = self.config.SUBSCRIPTION_PRICES[tier]
            
            subscription_data = {
                "customer_id": customer["id"],
                "plan_id": f"eliteforge_{tier.value}",
                "amount": price_cents,
                "currency": "USD",
                "interval": "month",
                "interval_count": 1,
                "trial_period_days": 7 if tier != SubscriptionTier.FREE else 0,
                "payment_method_id": payment_method_id,
                "metadata": {
                    "user_id": str(user_id),
                    "tier": tier.value,
                    "platform": "EliteForge AI"
                }
            }
            
            response = await self.client.post("/subscriptions", json=subscription_data)
            response.raise_for_status()
            
            payoneer_subscription = response.json()
            
            # Create local subscription record
            subscription = await self._create_local_subscription(
                user_id, tier, payoneer_subscription
            )
            
            logger.info(f"Created subscription {subscription.id} for user {user_id}")
            
            return {
                "subscription_id": str(subscription.id),
                "payoneer_subscription_id": payoneer_subscription["id"],
                "tier": tier.value,
                "status": subscription.status.value,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
                "trial_end": payoneer_subscription.get("trial_end"),
                "amount": price_cents,
                "currency": "USD"
            }
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Subscription creation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="SUBSCRIPTION_CREATION_FAILED",
                message=f"Failed to create subscription: {str(e)}"
            )
    
    async def process_payment(
        self,
        user_id: UUID,
        amount_cents: int,
        description: str,
        payment_method_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process a one-time payment"""
        try:
            user = await self.user_service.get_user_by_id(user_id)
            if not user:
                raise CustomException(
                    status_code=404,
                    error_code="USER_NOT_FOUND",
                    message="User not found"
                )
            
            # Get or create customer
            customer = await self._get_or_create_customer(user)
            
            # Create payment intent
            payment_data = {
                "customer_id": customer["id"],
                "amount": amount_cents,
                "currency": "USD",
                "description": description,
                "payment_method_id": payment_method_id,
                "confirm": True,
                "metadata": {
                    "user_id": str(user_id),
                    "platform": "EliteForge AI",
                    **(metadata or {})
                }
            }
            
            response = await self.client.post("/payment_intents", json=payment_data)
            response.raise_for_status()
            
            payment_intent = response.json()
            
            # Create local payment record
            payment = await self._create_local_payment(
                user_id, amount_cents, description, payment_intent
            )
            
            logger.info(f"Processed payment {payment.id} for user {user_id}: ${amount_cents/100:.2f}")
            
            return {
                "payment_id": str(payment.id),
                "payoneer_payment_id": payment_intent["id"],
                "amount": amount_cents,
                "currency": "USD",
                "status": payment_intent["status"],
                "description": description
            }
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Payment processing failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="PAYMENT_PROCESSING_FAILED",
                message=f"Payment processing failed: {str(e)}"
            )
    
    async def cancel_subscription(self, subscription_id: UUID) -> Dict[str, Any]:
        """Cancel a subscription"""
        try:
            # Get local subscription
            subscription = await self._get_subscription_by_id(subscription_id)
            if not subscription:
                raise CustomException(
                    status_code=404,
                    error_code="SUBSCRIPTION_NOT_FOUND",
                    message="Subscription not found"
                )
            
            # Cancel with Payoneer
            if subscription.payoneer_subscription_id:
                response = await self.client.delete(
                    f"/subscriptions/{subscription.payoneer_subscription_id}"
                )
                response.raise_for_status()
            
            # Update local subscription
            subscription.status = SubscriptionStatus.CANCELLED
            subscription.cancelled_at = datetime.utcnow()
            subscription.cancel_at_period_end = True
            
            await self.db.commit()
            
            logger.info(f"Cancelled subscription {subscription_id}")
            
            return {
                "subscription_id": str(subscription_id),
                "status": "cancelled",
                "cancelled_at": subscription.cancelled_at.isoformat()
            }
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Subscription cancellation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="SUBSCRIPTION_CANCELLATION_FAILED",
                message=f"Failed to cancel subscription: {str(e)}"
            )
    
    async def upgrade_subscription(
        self,
        subscription_id: UUID,
        new_tier: SubscriptionTier
    ) -> Dict[str, Any]:
        """Upgrade/downgrade a subscription"""
        try:
            subscription = await self._get_subscription_by_id(subscription_id)
            if not subscription:
                raise CustomException(
                    status_code=404,
                    error_code="SUBSCRIPTION_NOT_FOUND",
                    message="Subscription not found"
                )
            
            old_tier = subscription.tier
            new_price = self.config.SUBSCRIPTION_PRICES[new_tier]
            
            # Update with Payoneer
            if subscription.payoneer_subscription_id:
                update_data = {
                    "plan_id": f"eliteforge_{new_tier.value}",
                    "amount": new_price,
                    "prorate": True
                }
                
                response = await self.client.patch(
                    f"/subscriptions/{subscription.payoneer_subscription_id}",
                    json=update_data
                )
                response.raise_for_status()
            
            # Update local subscription
            subscription.tier = new_tier
            subscription.monthly_request_limit = self.config.TIER_LIMITS[new_tier]
            
            await self.db.commit()
            
            logger.info(f"Upgraded subscription {subscription_id} from {old_tier.value} to {new_tier.value}")
            
            return {
                "subscription_id": str(subscription_id),
                "old_tier": old_tier.value,
                "new_tier": new_tier.value,
                "new_price": new_price,
                "effective_date": datetime.utcnow().isoformat()
            }
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Subscription upgrade failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="SUBSCRIPTION_UPGRADE_FAILED",
                message=f"Failed to upgrade subscription: {str(e)}"
            )
    
    async def handle_webhook(self, payload: bytes, signature: str) -> Dict[str, Any]:
        """Handle Payoneer webhook events"""
        try:
            # Verify webhook signature
            if not self._verify_webhook_signature(payload, signature):
                raise CustomException(
                    status_code=400,
                    error_code="INVALID_WEBHOOK_SIGNATURE",
                    message="Invalid webhook signature"
                )
            
            # Parse event
            event = json.loads(payload.decode('utf-8'))
            event_type = event.get("type")
            event_data = event.get("data", {})
            
            logger.info(f"Processing Payoneer webhook: {event_type}")
            
            # Handle different event types
            if event_type == "subscription.created":
                return await self._handle_subscription_created(event_data)
            elif event_type == "subscription.updated":
                return await self._handle_subscription_updated(event_data)
            elif event_type == "subscription.cancelled":
                return await self._handle_subscription_cancelled(event_data)
            elif event_type == "payment.succeeded":
                return await self._handle_payment_succeeded(event_data)
            elif event_type == "payment.failed":
                return await self._handle_payment_failed(event_data)
            elif event_type == "invoice.payment_succeeded":
                return await self._handle_invoice_payment_succeeded(event_data)
            elif event_type == "invoice.payment_failed":
                return await self._handle_invoice_payment_failed(event_data)
            else:
                logger.warning(f"Unhandled webhook event type: {event_type}")
                return {"status": "ignored", "event_type": event_type}
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Webhook handling failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="WEBHOOK_PROCESSING_FAILED",
                message=f"Webhook processing failed: {str(e)}"
            )
    
    def _verify_webhook_signature(self, payload: bytes, signature: str) -> bool:
        """Verify Payoneer webhook signature"""
        try:
            expected_signature = hmac.new(
                self.webhook_secret.encode('utf-8'),
                payload,
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(f"sha256={expected_signature}", signature)
            
        except Exception as e:
            logger.error(f"Webhook signature verification failed: {str(e)}")
            return False
    
    async def _get_or_create_customer(self, user: User) -> Dict[str, Any]:
        """Get existing customer or create new one"""
        # Check if user already has a Payoneer customer ID
        if hasattr(user, 'payoneer_customer_id') and user.payoneer_customer_id:
            try:
                response = await self.client.get(f"/customers/{user.payoneer_customer_id}")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPStatusError:
                # Customer not found, create new one
                pass
        
        # Create new customer
        customer = await self.create_customer(user)
        
        # Update user with customer ID
        user.payoneer_customer_id = customer["id"]
        await self.db.commit()
        
        return customer
    
    async def _create_local_subscription(
        self,
        user_id: UUID,
        tier: SubscriptionTier,
        payoneer_subscription: Dict[str, Any]
    ) -> Subscription:
        """Create local subscription record"""
        subscription = Subscription(
            user_id=user_id,
            tier=tier,
            status=SubscriptionStatus.ACTIVE,
            payoneer_subscription_id=payoneer_subscription["id"],
            payoneer_customer_id=payoneer_subscription["customer_id"],
            current_period_start=datetime.utcnow(),
            current_period_end=datetime.utcnow() + timedelta(days=30),
            monthly_request_limit=self.config.TIER_LIMITS[tier],
            monthly_requests_used=0
        )
        
        self.db.add(subscription)
        await self.db.commit()
        await self.db.refresh(subscription)
        
        return subscription
    
    async def _create_local_payment(
        self,
        user_id: UUID,
        amount_cents: int,
        description: str,
        payment_intent: Dict[str, Any]
    ) -> Payment:
        """Create local payment record"""
        payment = Payment(
            user_id=user_id,
            payoneer_payment_intent_id=payment_intent["id"],
            amount_cents=amount_cents,
            currency="USD",
            status=payment_intent["status"],
            description=description,
            metadata=payment_intent.get("metadata", {}),
            paid_at=datetime.utcnow() if payment_intent["status"] == "succeeded" else None
        )
        
        self.db.add(payment)
        await self.db.commit()
        await self.db.refresh(payment)
        
        return payment
    
    # Webhook event handlers
    async def _handle_subscription_created(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription created webhook"""
        logger.info(f"Subscription created: {data.get('id')}")
        return {"status": "processed", "action": "subscription_created"}
    
    async def _handle_subscription_updated(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription updated webhook"""
        logger.info(f"Subscription updated: {data.get('id')}")
        return {"status": "processed", "action": "subscription_updated"}
    
    async def _handle_subscription_cancelled(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription cancelled webhook"""
        logger.info(f"Subscription cancelled: {data.get('id')}")
        return {"status": "processed", "action": "subscription_cancelled"}
    
    async def _handle_payment_succeeded(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle payment succeeded webhook"""
        logger.info(f"Payment succeeded: {data.get('id')}")
        return {"status": "processed", "action": "payment_succeeded"}
    
    async def _handle_payment_failed(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle payment failed webhook"""
        logger.info(f"Payment failed: {data.get('id')}")
        return {"status": "processed", "action": "payment_failed"}
    
    async def _handle_invoice_payment_succeeded(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle invoice payment succeeded webhook"""
        logger.info(f"Invoice payment succeeded: {data.get('id')}")
        return {"status": "processed", "action": "invoice_payment_succeeded"}
    
    async def _handle_invoice_payment_failed(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle invoice payment failed webhook"""
        logger.info(f"Invoice payment failed: {data.get('id')}")
        return {"status": "processed", "action": "invoice_payment_failed"}
    
    async def _get_subscription_by_id(self, subscription_id: UUID) -> Optional[Subscription]:
        """Get subscription by ID"""
        from sqlalchemy import select
        
        result = await self.db.execute(
            select(Subscription).where(Subscription.id == subscription_id)
        )
        return result.scalar_one_or_none()
    
    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()


__all__ = ["PayoneerService", "PayoneerConfig"]
