"""
EliteForge AI - Webhook Processing Service
Advanced webhook event processing and business logic handling
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, func, select, update
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.exceptions import CustomException
from app.db.models import (
    CreditTransaction, Payment, Subscription, SubscriptionStatus, 
    User, UsageRecord, WebhookEvent
)
from app.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class WebhookProcessor:
    """Advanced webhook event processing"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.notification_service = NotificationService()
        
        # Event handlers mapping
        self.event_handlers = {
            # Payoneer events
            "subscription.created": self._handle_subscription_created,
            "subscription.updated": self._handle_subscription_updated,
            "subscription.cancelled": self._handle_subscription_cancelled,
            "subscription.trial_will_end": self._handle_trial_ending,
            "payment.succeeded": self._handle_payment_succeeded,
            "payment.failed": self._handle_payment_failed,
            "invoice.payment_succeeded": self._handle_invoice_payment_succeeded,
            "invoice.payment_failed": self._handle_invoice_payment_failed,
            "customer.created": self._handle_customer_created,
            "customer.updated": self._handle_customer_updated,
            
            # Stripe events (fallback)
            "customer.subscription.created": self._handle_stripe_subscription_created,
            "customer.subscription.updated": self._handle_stripe_subscription_updated,
            "customer.subscription.deleted": self._handle_stripe_subscription_deleted,
            "invoice.payment_succeeded": self._handle_stripe_invoice_payment_succeeded,
            "invoice.payment_failed": self._handle_stripe_invoice_payment_failed,
        }
    
    async def handle_stripe_webhook(self, payload: bytes, signature: str) -> Dict[str, Any]:
        """Handle Stripe webhook events"""
        try:
            import stripe
            
            # Verify webhook signature
            event = stripe.Webhook.construct_event(
                payload, signature, settings.STRIPE_WEBHOOK_SECRET
            )
            
            # Log webhook event
            await self._log_webhook_event("stripe", event["type"], event["data"])
            
            # Process event
            event_type = event["type"]
            event_data = event["data"]["object"]
            
            if event_type in self.event_handlers:
                result = await self.event_handlers[event_type](event_data)
                logger.info(f"Processed Stripe webhook: {event_type}")
                return result
            else:
                logger.warning(f"Unhandled Stripe webhook event: {event_type}")
                return {"status": "ignored", "event_type": event_type}
                
        except Exception as e:
            logger.error(f"Stripe webhook processing failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="STRIPE_WEBHOOK_FAILED",
                message=f"Stripe webhook processing failed: {str(e)}"
            )
    
    async def aggregate_usage_data(self) -> Dict[str, Any]:
        """Aggregate usage data for analytics and billing"""
        try:
            logger.info("Starting usage data aggregation")
            
            # Get yesterday's date for aggregation
            yesterday = datetime.utcnow().date() - timedelta(days=1)
            start_time = datetime.combine(yesterday, datetime.min.time())
            end_time = datetime.combine(yesterday, datetime.max.time())
            
            # Aggregate usage by user
            result = await self.db.execute(
                select(
                    UsageRecord.user_id,
                    UsageRecord.request_type,
                    func.count(UsageRecord.id).label("request_count"),
                    func.sum(UsageRecord.cost_cents).label("total_cost"),
                    func.sum(UsageRecord.tokens_used).label("total_tokens")
                )
                .where(
                    and_(
                        UsageRecord.created_at >= start_time,
                        UsageRecord.created_at <= end_time
                    )
                )
                .group_by(UsageRecord.user_id, UsageRecord.request_type)
            )
            
            aggregations = result.all()
            
            # Process aggregations
            processed_users = set()
            total_requests = 0
            total_cost = 0
            
            for agg in aggregations:
                processed_users.add(agg.user_id)
                total_requests += agg.request_count
                total_cost += agg.total_cost or 0
                
                # Could store daily aggregations in a separate table
                # daily_usage = DailyUsageAggregate(
                #     user_id=agg.user_id,
                #     date=yesterday,
                #     request_type=agg.request_type,
                #     request_count=agg.request_count,
                #     total_cost_cents=agg.total_cost or 0,
                #     total_tokens=agg.total_tokens or 0
                # )
                # self.db.add(daily_usage)
            
            # Check for users approaching limits
            await self._check_usage_limits(processed_users)
            
            # Check for low credit balances
            await self._check_low_credit_balances(processed_users)
            
            await self.db.commit()
            
            logger.info(f"Usage aggregation completed: {len(processed_users)} users, {total_requests} requests, ${total_cost/100:.2f} total cost")
            
            return {
                "date": yesterday.isoformat(),
                "users_processed": len(processed_users),
                "total_requests": total_requests,
                "total_cost_cents": total_cost,
                "total_cost_dollars": total_cost / 100
            }
            
        except Exception as e:
            logger.error(f"Usage aggregation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="USAGE_AGGREGATION_FAILED",
                message=f"Usage aggregation failed: {str(e)}"
            )
    
    # Payoneer webhook handlers
    async def _handle_subscription_created(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription created event"""
        try:
            subscription_id = data.get("id")
            customer_id = data.get("customer_id")
            
            # Find user by customer ID
            user = await self._get_user_by_customer_id(customer_id)
            if user:
                # Send welcome email
                await self.notification_service.send_subscription_welcome(
                    user.email, user.full_name or user.username
                )
                
                logger.info(f"Subscription created for user {user.id}: {subscription_id}")
            
            return {"status": "processed", "action": "subscription_created"}
            
        except Exception as e:
            logger.error(f"Subscription created handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_subscription_updated(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription updated event"""
        try:
            subscription_id = data.get("id")
            status = data.get("status")
            
            # Update local subscription if needed
            result = await self.db.execute(
                update(Subscription)
                .where(Subscription.payoneer_subscription_id == subscription_id)
                .values(updated_at=datetime.utcnow())
            )
            
            await self.db.commit()
            
            logger.info(f"Subscription updated: {subscription_id}, status: {status}")
            
            return {"status": "processed", "action": "subscription_updated"}
            
        except Exception as e:
            logger.error(f"Subscription updated handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_subscription_cancelled(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle subscription cancelled event"""
        try:
            subscription_id = data.get("id")
            customer_id = data.get("customer_id")
            
            # Update local subscription
            result = await self.db.execute(
                update(Subscription)
                .where(Subscription.payoneer_subscription_id == subscription_id)
                .values(
                    status=SubscriptionStatus.CANCELLED,
                    cancelled_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            )
            
            await self.db.commit()
            
            # Send cancellation email
            user = await self._get_user_by_customer_id(customer_id)
            if user:
                await self.notification_service.send_subscription_cancelled(
                    user.email, user.full_name or user.username
                )
            
            logger.info(f"Subscription cancelled: {subscription_id}")
            
            return {"status": "processed", "action": "subscription_cancelled"}
            
        except Exception as e:
            logger.error(f"Subscription cancelled handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_trial_ending(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle trial ending notification"""
        try:
            subscription_id = data.get("id")
            customer_id = data.get("customer_id")
            trial_end = data.get("trial_end")
            
            # Send trial ending notification
            user = await self._get_user_by_customer_id(customer_id)
            if user:
                await self.notification_service.send_trial_ending_reminder(
                    user.email, user.full_name or user.username, trial_end
                )
            
            logger.info(f"Trial ending notification sent for subscription: {subscription_id}")
            
            return {"status": "processed", "action": "trial_ending_notification"}
            
        except Exception as e:
            logger.error(f"Trial ending handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_payment_succeeded(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle successful payment"""
        try:
            payment_id = data.get("id")
            customer_id = data.get("customer_id")
            amount = data.get("amount")
            
            # Update local payment record
            result = await self.db.execute(
                update(Payment)
                .where(Payment.payoneer_payment_intent_id == payment_id)
                .values(
                    status="succeeded",
                    paid_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            )
            
            await self.db.commit()
            
            # Send payment confirmation
            user = await self._get_user_by_customer_id(customer_id)
            if user:
                await self.notification_service.send_payment_confirmation(
                    user.email, user.full_name or user.username, amount / 100
                )
            
            logger.info(f"Payment succeeded: {payment_id}, amount: ${amount/100:.2f}")
            
            return {"status": "processed", "action": "payment_succeeded"}
            
        except Exception as e:
            logger.error(f"Payment succeeded handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_payment_failed(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle failed payment"""
        try:
            payment_id = data.get("id")
            customer_id = data.get("customer_id")
            failure_reason = data.get("failure_reason")
            
            # Update local payment record
            result = await self.db.execute(
                update(Payment)
                .where(Payment.payoneer_payment_intent_id == payment_id)
                .values(
                    status="failed",
                    failure_reason=failure_reason,
                    updated_at=datetime.utcnow()
                )
            )
            
            await self.db.commit()
            
            # Send payment failure notification
            user = await self._get_user_by_customer_id(customer_id)
            if user:
                await self.notification_service.send_payment_failed(
                    user.email, user.full_name or user.username, failure_reason
                )
            
            logger.warning(f"Payment failed: {payment_id}, reason: {failure_reason}")
            
            return {"status": "processed", "action": "payment_failed"}
            
        except Exception as e:
            logger.error(f"Payment failed handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_invoice_payment_succeeded(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle successful invoice payment"""
        try:
            invoice_id = data.get("id")
            subscription_id = data.get("subscription")
            amount_paid = data.get("amount_paid")
            
            logger.info(f"Invoice payment succeeded: {invoice_id}, subscription: {subscription_id}")
            
            return {"status": "processed", "action": "invoice_payment_succeeded"}
            
        except Exception as e:
            logger.error(f"Invoice payment succeeded handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_invoice_payment_failed(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle failed invoice payment"""
        try:
            invoice_id = data.get("id")
            subscription_id = data.get("subscription")
            
            logger.warning(f"Invoice payment failed: {invoice_id}, subscription: {subscription_id}")
            
            return {"status": "processed", "action": "invoice_payment_failed"}
            
        except Exception as e:
            logger.error(f"Invoice payment failed handler failed: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    async def _handle_customer_created(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle customer created event"""
        customer_id = data.get("id")
        logger.info(f"Customer created: {customer_id}")
        return {"status": "processed", "action": "customer_created"}
    
    async def _handle_customer_updated(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle customer updated event"""
        customer_id = data.get("id")
        logger.info(f"Customer updated: {customer_id}")
        return {"status": "processed", "action": "customer_updated"}
    
    # Stripe webhook handlers (fallback)
    async def _handle_stripe_subscription_created(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Stripe subscription created"""
        return await self._handle_subscription_created(data)
    
    async def _handle_stripe_subscription_updated(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Stripe subscription updated"""
        return await self._handle_subscription_updated(data)
    
    async def _handle_stripe_subscription_deleted(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Stripe subscription deleted"""
        return await self._handle_subscription_cancelled(data)
    
    async def _handle_stripe_invoice_payment_succeeded(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Stripe invoice payment succeeded"""
        return await self._handle_invoice_payment_succeeded(data)
    
    async def _handle_stripe_invoice_payment_failed(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle Stripe invoice payment failed"""
        return await self._handle_invoice_payment_failed(data)
    
    # Helper methods
    async def _log_webhook_event(self, provider: str, event_type: str, data: Dict[str, Any]):
        """Log webhook event for debugging"""
        webhook_event = WebhookEvent(
            provider=provider,
            event_type=event_type,
            data=data,
            processed_at=datetime.utcnow()
        )
        
        self.db.add(webhook_event)
        await self.db.commit()
    
    async def _get_user_by_customer_id(self, customer_id: str) -> Optional[User]:
        """Get user by payment provider customer ID"""
        result = await self.db.execute(
            select(User).where(
                (User.payoneer_customer_id == customer_id) |
                (User.stripe_customer_id == customer_id)
            )
        )
        return result.scalar_one_or_none()
    
    async def _check_usage_limits(self, user_ids: set):
        """Check users approaching usage limits"""
        for user_id in user_ids:
            from app.services.subscription_service import SubscriptionManager
            
            subscription_manager = SubscriptionManager(self.db)
            subscription = await subscription_manager.get_user_subscription(user_id)
            
            if subscription and subscription.monthly_request_limit > 0:
                usage_percentage = (subscription.monthly_requests_used / subscription.monthly_request_limit) * 100
                
                if usage_percentage >= 80:  # 80% threshold
                    user = await self._get_user_by_id(user_id)
                    if user:
                        await self.notification_service.send_usage_limit_warning(
                            user.email, user.full_name or user.username, usage_percentage
                        )
    
    async def _check_low_credit_balances(self, user_ids: set):
        """Check users with low credit balances"""
        for user_id in user_ids:
            from app.services.billing_service import BillingService
            
            billing_service = BillingService(self.db)
            credits = await billing_service.get_user_credits(user_id)
            
            if credits["balance_cents"] < 500:  # Less than $5
                user = await self._get_user_by_id(user_id)
                if user:
                    await self.notification_service.send_low_credit_warning(
                        user.email, user.full_name or user.username, credits["balance_dollars"]
                    )
    
    async def _get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get user by ID"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()


__all__ = ["WebhookProcessor"]
