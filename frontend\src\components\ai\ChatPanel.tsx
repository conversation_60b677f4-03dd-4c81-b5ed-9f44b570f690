import React, { useState, useRef, useEffect } from 'react'
import { useMutation } from '@tanstack/react-query'
import { 
  ChatBubbleLeftRightIcon, 
  PaperAirplaneIcon,
  TrashIcon,
  ClipboardDocumentIcon,
  UserIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline'
import { aiApi } from '../../lib/api'

interface ChatPanelProps {
  onGenerationStart: () => void
  onGenerationComplete: () => void
  onGenerationError: () => void
  isGenerating: boolean
  subscription?: any
  models: any[]
}

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

const chatModels = [
  { id: 'llama-2-7b-chat', name: 'Llama 2 7B Chat', description: 'Conversational AI' },
  { id: 'llama-2-13b-chat', name: 'Llama 2 13B Chat', description: 'Advanced conversation (Advanced+)' },
  { id: 'custom-chat', name: 'Custom Chat Model', description: 'Specialized chat (Enterprise)' },
]

const chatPresets = [
  {
    name: 'General Assistant',
    systemPrompt: 'You are a helpful, harmless, and honest AI assistant.',
  },
  {
    name: 'Code Helper',
    systemPrompt: 'You are an expert programmer who helps with coding questions and debugging.',
  },
  {
    name: 'Creative Writer',
    systemPrompt: 'You are a creative writing assistant who helps with storytelling and creative content.',
  },
  {
    name: 'Technical Explainer',
    systemPrompt: 'You are a technical expert who explains complex concepts in simple terms.',
  },
]

export default function ChatPanel({
  onGenerationStart,
  onGenerationComplete,
  onGenerationError,
  isGenerating,
  subscription,
  models
}: ChatPanelProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [currentMessage, setCurrentMessage] = useState('')
  const [selectedModel, setSelectedModel] = useState('llama-2-7b-chat')
  const [systemPrompt, setSystemPrompt] = useState('You are a helpful, harmless, and honest AI assistant.')
  const [showSystemPrompt, setShowSystemPrompt] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  const chatMutation = useMutation({
    mutationFn: (data: any) => aiApi.chatCompletion(data.messages, data.model),
    onMutate: () => {
      onGenerationStart()
    },
    onSuccess: (response) => {
      const assistantMessage: ChatMessage = {
        id: Date.now().toString() + '-assistant',
        role: 'assistant',
        content: response.data.result,
        timestamp: new Date(),
      }
      setMessages(prev => [...prev, assistantMessage])
      onGenerationComplete()
    },
    onError: (error) => {
      console.error('Chat completion failed:', error)
      onGenerationError()
    },
  })

  const handleSendMessage = () => {
    if (!currentMessage.trim() || isGenerating) return

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString() + '-user',
      role: 'user',
      content: currentMessage.trim(),
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    
    // Prepare messages for API
    const apiMessages = [
      { role: 'system', content: systemPrompt },
      ...messages.map(msg => ({ role: msg.role, content: msg.content })),
      { role: 'user', content: currentMessage.trim() }
    ]

    chatMutation.mutate({
      messages: apiMessages,
      model: selectedModel,
    })

    setCurrentMessage('')
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleClearChat = () => {
    setMessages([])
  }

  const handleCopyMessage = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  const handlePresetSelect = (preset: typeof chatPresets[0]) => {
    setSystemPrompt(preset.systemPrompt)
  }

  const canUseModel = (modelId: string) => {
    if (!subscription) return false
    const tier = subscription.tier.toLowerCase()
    
    if (modelId.includes('13b') && !['advanced', 'enterprise'].includes(tier)) return false
    if (modelId.includes('custom') && tier !== 'enterprise') return false
    
    return true
  }

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  useEffect(() => {
    if (!isGenerating && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isGenerating])

  return (
    <div className="p-6 h-[600px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <ChatBubbleLeftRightIcon className="h-6 w-6 text-orange-500 mr-3" />
          <h3 className="text-lg font-semibold text-gray-900">AI Chat</h3>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowSystemPrompt(!showSystemPrompt)}
            className="px-3 py-1 text-sm text-primary-600 hover:text-primary-700 border border-primary-200 rounded-lg hover:bg-primary-50"
          >
            System Prompt
          </button>
          <button
            onClick={handleClearChat}
            disabled={messages.length === 0}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Clear chat"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* System Prompt Configuration */}
      {showSystemPrompt && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <label className="text-sm font-medium text-gray-700">System Prompt</label>
            <div className="flex space-x-2">
              {chatPresets.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => handlePresetSelect(preset)}
                  className="px-2 py-1 text-xs text-primary-600 hover:text-primary-700 border border-primary-200 rounded hover:bg-primary-50"
                >
                  {preset.name}
                </button>
              ))}
            </div>
          </div>
          <textarea
            value={systemPrompt}
            onChange={(e) => setSystemPrompt(e.target.value)}
            disabled={isGenerating}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            rows={2}
            placeholder="Set the AI's behavior and personality..."
          />
        </div>
      )}

      {/* Model Selection */}
      <div className="mb-4">
        <select
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
          disabled={isGenerating}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
        >
          {chatModels.map((model) => (
            <option 
              key={model.id} 
              value={model.id}
              disabled={!canUseModel(model.id)}
            >
              {model.name} - {model.description}
              {!canUseModel(model.id) ? ' (Upgrade Required)' : ''}
            </option>
          ))}
        </select>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto border border-gray-200 rounded-lg p-4 mb-4 bg-white">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <ChatBubbleLeftRightIcon className="h-12 w-12 mx-auto mb-2" />
              <p>Start a conversation with the AI</p>
              <p className="text-sm mt-1">Type your message below to begin</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                      message.role === 'user' ? 'bg-primary-700' : 'bg-gray-200'
                    }`}>
                      {message.role === 'user' ? (
                        <UserIcon className="h-4 w-4 text-white" />
                      ) : (
                        <CpuChipIcon className="h-4 w-4 text-gray-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="prose prose-sm max-w-none">
                        <pre className="whitespace-pre-wrap font-sans text-sm">
                          {message.content}
                        </pre>
                      </div>
                      <div className="flex items-center justify-between mt-2">
                        <span className={`text-xs ${
                          message.role === 'user' ? 'text-primary-200' : 'text-gray-500'
                        }`}>
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                        <button
                          onClick={() => handleCopyMessage(message.content)}
                          className={`p-1 rounded hover:bg-opacity-20 ${
                            message.role === 'user' 
                              ? 'text-primary-200 hover:bg-white' 
                              : 'text-gray-400 hover:bg-gray-300'
                          }`}
                          title="Copy message"
                        >
                          <ClipboardDocumentIcon className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            {isGenerating && (
              <div className="flex justify-start">
                <div className="bg-gray-100 text-gray-900 rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
                      <CpuChipIcon className="h-4 w-4 text-gray-600" />
                    </div>
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Message Input */}
      <div className="flex space-x-2">
        <textarea
          ref={inputRef}
          value={currentMessage}
          onChange={(e) => setCurrentMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={isGenerating}
          placeholder="Type your message... (Press Enter to send, Shift+Enter for new line)"
          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50 resize-none"
          rows={2}
        />
        <button
          onClick={handleSendMessage}
          disabled={!currentMessage.trim() || isGenerating}
          className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          <PaperAirplaneIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  )
}
