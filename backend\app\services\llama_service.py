"""
EliteForge AI - Llama/CodeLlama Service
Text and code generation using Llama models with Hugging Face Transformers
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Generator
from uuid import UUID
import json

import torch
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    LlamaTokenizer,
    LlamaForCausalLM,
    CodeLlamaTokenizer,
    TextStreamer,
    GenerationConfig,
    BitsAndBytesConfig
)
from transformers.utils import is_flash_attn_2_available

from app.core.config import settings
from app.core.exceptions import CustomException
from app.utils.text_processing import clean_generated_text, format_code_output

logger = logging.getLogger(__name__)


class LlamaConfig:
    """Configuration for Llama and CodeLlama models"""
    
    MODELS = {
        "llama-2-7b": {
            "model_id": "meta-llama/Llama-2-7b-chat-hf",
            "description": "Llama 2 7B Chat - General purpose conversational AI",
            "tier_required": "standard",
            "memory_usage": 6144,  # 6GB
            "max_tokens": 4096,
            "context_length": 4096,
            "model_type": "chat",
        },
        "llama-2-13b": {
            "model_id": "meta-llama/Llama-2-13b-chat-hf",
            "description": "Llama 2 13B Chat - Enhanced conversational AI",
            "tier_required": "advanced",
            "memory_usage": 12288,  # 12GB
            "max_tokens": 4096,
            "context_length": 4096,
            "model_type": "chat",
        },
        "code-llama-7b": {
            "model_id": "codellama/CodeLlama-7b-Python-hf",
            "description": "CodeLlama 7B Python - Specialized for Python code generation",
            "tier_required": "standard",
            "memory_usage": 6144,  # 6GB
            "max_tokens": 16384,
            "context_length": 16384,
            "model_type": "code",
        },
        "code-llama-13b": {
            "model_id": "codellama/CodeLlama-13b-Python-hf",
            "description": "CodeLlama 13B Python - Advanced Python code generation",
            "tier_required": "advanced",
            "memory_usage": 12288,  # 12GB
            "max_tokens": 16384,
            "context_length": 16384,
            "model_type": "code",
        },
        "code-llama-instruct": {
            "model_id": "codellama/CodeLlama-7b-Instruct-hf",
            "description": "CodeLlama 7B Instruct - Instruction-following code model",
            "tier_required": "standard",
            "memory_usage": 6144,  # 6GB
            "max_tokens": 16384,
            "context_length": 16384,
            "model_type": "code_instruct",
        },
    }
    
    DEFAULT_GENERATION_PARAMS = {
        "max_new_tokens": 512,
        "temperature": 0.7,
        "top_p": 0.9,
        "top_k": 50,
        "repetition_penalty": 1.1,
        "do_sample": True,
        "pad_token_id": None,  # Will be set based on tokenizer
        "eos_token_id": None,  # Will be set based on tokenizer
    }
    
    # System prompts for different model types
    SYSTEM_PROMPTS = {
        "chat": "You are a helpful, respectful and honest assistant. Always answer as helpfully as possible, while being safe.",
        "code": "You are an expert programmer. Generate clean, efficient, and well-documented code.",
        "code_instruct": "You are a coding assistant. Follow the instructions carefully and provide complete, working code solutions.",
    }


class LlamaService:
    """Llama and CodeLlama text/code generation service"""
    
    def __init__(self):
        self.device = self._get_optimal_device()
        self.loaded_models = {}
        self.config = LlamaConfig()
        
        # Quantization settings for memory efficiency
        self.use_quantization = settings.USE_MODEL_QUANTIZATION
        self.quantization_config = self._get_quantization_config()
        
        logger.info(f"LlamaService initialized on device: {self.device}")
    
    def _get_optimal_device(self) -> str:
        """Determine optimal device for Llama inference"""
        if torch.cuda.is_available():
            return f"cuda:{torch.cuda.current_device()}"
        elif torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"
    
    def _get_quantization_config(self) -> Optional[BitsAndBytesConfig]:
        """Configure quantization for memory efficiency"""
        if not self.use_quantization or not torch.cuda.is_available():
            return None
        
        try:
            return BitsAndBytesConfig(
                load_in_4bit=True,
                bnb_4bit_compute_dtype=torch.float16,
                bnb_4bit_use_double_quant=True,
                bnb_4bit_quant_type="nf4"
            )
        except ImportError:
            logger.warning("BitsAndBytesConfig not available, disabling quantization")
            return None
    
    async def load_model(self, model_name: str = "llama-2-7b") -> Dict[str, Any]:
        """Load Llama model with optimizations"""
        
        if model_name in self.loaded_models:
            logger.info(f"Using cached Llama model: {model_name}")
            return self.loaded_models[model_name]
        
        model_config = self.config.MODELS.get(model_name)
        if not model_config:
            raise CustomException(
                status_code=400,
                error_code="LLAMA_MODEL_NOT_FOUND",
                message=f"Llama model {model_name} not found"
            )
        
        try:
            logger.info(f"Loading Llama model: {model_name}")
            start_time = time.time()
            
            model_id = model_config["model_id"]
            
            # Load tokenizer
            if "CodeLlama" in model_id:
                tokenizer = CodeLlamaTokenizer.from_pretrained(
                    model_id,
                    cache_dir=settings.MODEL_CACHE_DIR,
                    use_fast=True
                )
            else:
                tokenizer = AutoTokenizer.from_pretrained(
                    model_id,
                    cache_dir=settings.MODEL_CACHE_DIR,
                    use_fast=True
                )
            
            # Set padding token if not present
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            # Load model with optimizations
            model_kwargs = {
                "cache_dir": settings.MODEL_CACHE_DIR,
                "torch_dtype": torch.float16 if self.device.startswith("cuda") else torch.float32,
                "device_map": "auto" if self.device.startswith("cuda") else None,
                "trust_remote_code": True,
            }
            
            # Add quantization if enabled
            if self.quantization_config:
                model_kwargs["quantization_config"] = self.quantization_config
            
            # Add Flash Attention 2 if available
            if is_flash_attn_2_available() and self.device.startswith("cuda"):
                model_kwargs["attn_implementation"] = "flash_attention_2"
                logger.debug("Using Flash Attention 2")
            
            model = AutoModelForCausalLM.from_pretrained(model_id, **model_kwargs)
            
            # Move to device if not using device_map
            if not model_kwargs.get("device_map"):
                model = model.to(self.device)
            
            # Enable optimizations
            if hasattr(model, "gradient_checkpointing_enable"):
                model.gradient_checkpointing_enable()
            
            load_time = time.time() - start_time
            logger.info(f"Llama model {model_name} loaded in {load_time:.2f}s")
            
            # Create generation config
            generation_config = GenerationConfig(
                **self.config.DEFAULT_GENERATION_PARAMS,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id,
                max_length=model_config["context_length"],
            )
            
            model_dict = {
                "model": model,
                "tokenizer": tokenizer,
                "generation_config": generation_config,
                "config": model_config,
                "load_time": load_time
            }
            
            self.loaded_models[model_name] = model_dict
            return model_dict
            
        except Exception as e:
            logger.error(f"Failed to load Llama model {model_name}: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="LLAMA_MODEL_LOAD_FAILED",
                message=f"Failed to load Llama model: {str(e)}"
            )
    
    async def generate_text(
        self,
        prompt: str,
        model_name: str = "llama-2-7b",
        parameters: Dict[str, Any] = None,
        system_prompt: str = None
    ) -> Dict[str, Any]:
        """Generate text using Llama models"""
        
        # Load model
        model_dict = await self.load_model(model_name)
        model = model_dict["model"]
        tokenizer = model_dict["tokenizer"]
        model_config = model_dict["config"]
        
        # Merge parameters
        gen_params = {**self.config.DEFAULT_GENERATION_PARAMS, **(parameters or {})}
        gen_params = self._validate_generation_parameters(gen_params, model_config)
        
        # Format prompt based on model type
        formatted_prompt = self._format_prompt(
            prompt, 
            model_config["model_type"], 
            system_prompt or self.config.SYSTEM_PROMPTS.get(model_config["model_type"], "")
        )
        
        try:
            logger.info(f"Generating text with {model_name}: {prompt[:100]}...")
            start_time = time.time()
            
            # Tokenize input
            inputs = tokenizer(
                formatted_prompt,
                return_tensors="pt",
                truncation=True,
                max_length=model_config["context_length"] - gen_params["max_new_tokens"]
            ).to(model.device)
            
            # Generate
            with torch.inference_mode():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=gen_params["max_new_tokens"],
                    temperature=gen_params["temperature"],
                    top_p=gen_params["top_p"],
                    top_k=gen_params["top_k"],
                    repetition_penalty=gen_params["repetition_penalty"],
                    do_sample=gen_params["do_sample"],
                    pad_token_id=tokenizer.pad_token_id,
                    eos_token_id=tokenizer.eos_token_id,
                )
            
            generation_time = time.time() - start_time
            
            # Decode output
            generated_text = tokenizer.decode(
                outputs[0][inputs.input_ids.shape[1]:], 
                skip_special_tokens=True
            )
            
            # Post-process based on model type
            if model_config["model_type"] in ["code", "code_instruct"]:
                generated_text = await format_code_output(generated_text)
            else:
                generated_text = await clean_generated_text(generated_text)
            
            # Calculate tokens
            input_tokens = inputs.input_ids.shape[1]
            output_tokens = outputs[0].shape[0] - input_tokens
            
            logger.info(f"Generated {output_tokens} tokens in {generation_time:.2f}s")
            
            return {
                "generated_text": generated_text,
                "model_name": model_name,
                "model_type": model_config["model_type"],
                "generation_time": generation_time,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "tokens_per_second": output_tokens / generation_time if generation_time > 0 else 0,
                "parameters": gen_params,
                "prompt": prompt,
                "formatted_prompt": formatted_prompt
            }
            
        except Exception as e:
            logger.error(f"Text generation failed with {model_name}: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="TEXT_GENERATION_FAILED",
                message=f"Text generation failed: {str(e)}"
            )
    
    async def generate_code(
        self,
        prompt: str,
        language: str = "python",
        model_name: str = "code-llama-7b",
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate code using CodeLlama models"""
        
        # Use code-specific model if not specified
        if not model_name.startswith("code-llama"):
            model_name = "code-llama-7b"
        
        # Format code generation prompt
        code_prompt = self._format_code_prompt(prompt, language)
        
        # Generate with code-specific parameters
        code_params = {
            "temperature": 0.1,  # Lower temperature for more deterministic code
            "top_p": 0.95,
            "repetition_penalty": 1.05,
            **(parameters or {})
        }
        
        result = await self.generate_text(
            code_prompt,
            model_name=model_name,
            parameters=code_params,
            system_prompt=self.config.SYSTEM_PROMPTS["code"]
        )
        
        # Add code-specific metadata
        result.update({
            "language": language,
            "code_type": "generation",
            "original_prompt": prompt
        })
        
        return result
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model_name: str = "llama-2-7b",
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Chat completion using Llama chat models"""
        
        # Format messages into a single prompt
        chat_prompt = self._format_chat_messages(messages)
        
        result = await self.generate_text(
            chat_prompt,
            model_name=model_name,
            parameters=parameters,
            system_prompt=self.config.SYSTEM_PROMPTS["chat"]
        )
        
        # Add chat-specific metadata
        result.update({
            "chat_type": "completion",
            "message_count": len(messages),
            "messages": messages
        })
        
        return result
    
    def _format_prompt(self, prompt: str, model_type: str, system_prompt: str = "") -> str:
        """Format prompt based on model type"""
        if model_type == "chat":
            return f"<s>[INST] <<SYS>>\n{system_prompt}\n<</SYS>>\n\n{prompt} [/INST]"
        elif model_type == "code_instruct":
            return f"[INST] {system_prompt}\n\n{prompt} [/INST]"
        else:
            return prompt
    
    def _format_code_prompt(self, prompt: str, language: str) -> str:
        """Format prompt for code generation"""
        return f"# {language.title()} code for: {prompt}\n\n```{language}\n"
    
    def _format_chat_messages(self, messages: List[Dict[str, str]]) -> str:
        """Format chat messages into Llama chat format"""
        formatted = ""
        for i, message in enumerate(messages):
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                continue  # System messages handled separately
            elif role == "user":
                if i == 0:
                    formatted += f"<s>[INST] {content} [/INST]"
                else:
                    formatted += f"<s>[INST] {content} [/INST]"
            elif role == "assistant":
                formatted += f" {content} </s>"
        
        return formatted
    
    def _validate_generation_parameters(self, params: Dict[str, Any], model_config: Dict) -> Dict[str, Any]:
        """Validate and sanitize generation parameters"""
        validated = params.copy()
        
        # Validate max_new_tokens
        max_context = model_config["context_length"]
        validated["max_new_tokens"] = max(1, min(max_context // 2, validated.get("max_new_tokens", 512)))
        
        # Validate temperature
        validated["temperature"] = max(0.01, min(2.0, validated.get("temperature", 0.7)))
        
        # Validate top_p
        validated["top_p"] = max(0.01, min(1.0, validated.get("top_p", 0.9)))
        
        # Validate top_k
        validated["top_k"] = max(1, min(200, validated.get("top_k", 50)))
        
        # Validate repetition_penalty
        validated["repetition_penalty"] = max(1.0, min(2.0, validated.get("repetition_penalty", 1.1)))
        
        return validated
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available Llama models"""
        models = []
        for name, config in self.config.MODELS.items():
            models.append({
                "name": name,
                "model_id": config["model_id"],
                "description": config["description"],
                "tier_required": config["tier_required"],
                "memory_usage_mb": config["memory_usage"],
                "max_tokens": config["max_tokens"],
                "context_length": config["context_length"],
                "model_type": config["model_type"]
            })
        return models
    
    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """Get detailed information about a specific model"""
        if model_name not in self.config.MODELS:
            raise CustomException(
                status_code=404,
                error_code="MODEL_NOT_FOUND",
                message=f"Model {model_name} not found"
            )
        
        config = self.config.MODELS[model_name]
        info = config.copy()
        
        # Add runtime information if model is loaded
        if model_name in self.loaded_models:
            model_dict = self.loaded_models[model_name]
            info.update({
                "loaded": True,
                "load_time": model_dict["load_time"],
                "device": str(model_dict["model"].device),
                "memory_footprint": self._get_model_memory_usage(model_dict["model"])
            })
        else:
            info["loaded"] = False
        
        return info
    
    def _get_model_memory_usage(self, model) -> Dict[str, float]:
        """Get model memory usage statistics"""
        try:
            if torch.cuda.is_available() and next(model.parameters()).is_cuda:
                return {
                    "gpu_memory_mb": torch.cuda.memory_allocated() / (1024 * 1024),
                    "gpu_reserved_mb": torch.cuda.memory_reserved() / (1024 * 1024)
                }
            else:
                # Estimate CPU memory usage
                param_size = sum(p.numel() * p.element_size() for p in model.parameters())
                return {
                    "cpu_memory_mb": param_size / (1024 * 1024)
                }
        except Exception:
            return {"memory_mb": 0}
    
    async def clear_cache(self):
        """Clear loaded models and free memory"""
        self.loaded_models.clear()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        import gc
        gc.collect()
        
        logger.info("Cleared Llama model cache")


# Global service instance
llama_service = LlamaService()

__all__ = ["LlamaService", "llama_service", "LlamaConfig"]
