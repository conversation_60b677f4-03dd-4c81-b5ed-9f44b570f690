import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { 
  SparklesIcon, 
  PhotoIcon, 
  CodeBracketIcon,
  CreditCardIcon,
  ChartBarIcon,
  ClockIcon,
  ArrowRightIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { useAuth, useSubscriptionTier } from '../lib/auth'
import { billingApi } from '../lib/api'
import { formatCurrency, formatDate } from '../lib/utils'
import LoadingSpinner from '../components/LoadingSpinner'

export default function DashboardPage() {
  const { user } = useAuth()
  const { tier, getRequestLimit, hasFeature } = useSubscriptionTier()

  // Fetch subscription data
  const { data: subscriptionData, isLoading: subscriptionLoading } = useQuery({
    queryKey: ['subscription'],
    queryFn: () => billingApi.getCurrentSubscription(),
  })

  // Fetch credits data
  const { data: creditsData, isLoading: creditsLoading } = useQuery({
    queryKey: ['credits'],
    queryFn: () => billingApi.getCredits(),
  })

  // Fetch usage report
  const { data: usageData, isLoading: usageLoading } = useQuery({
    queryKey: ['usage', 7], // Last 7 days
    queryFn: () => billingApi.getUsageReport(7),
  })

  const subscription = subscriptionData?.data.subscription
  const credits = creditsData?.data
  const usage = usageData?.data

  const quickActions = [
    {
      name: 'Generate Text',
      description: 'Create content with AI language models',
      icon: SparklesIcon,
      href: '/ai-studio?type=text',
      color: 'bg-blue-500',
      available: hasFeature('basic_text'),
    },
    {
      name: 'Generate Images',
      description: 'Create stunning visuals with AI',
      icon: PhotoIcon,
      href: '/ai-studio?type=image',
      color: 'bg-purple-500',
      available: hasFeature('basic_image'),
    },
    {
      name: 'Generate Code',
      description: 'Get AI assistance with coding',
      icon: CodeBracketIcon,
      href: '/ai-studio?type=code',
      color: 'bg-green-500',
      available: hasFeature('code_generation'),
    },
    {
      name: 'Manage Billing',
      description: 'View usage and manage subscription',
      icon: CreditCardIcon,
      href: '/billing',
      color: 'bg-orange-500',
      available: true,
    },
  ]

  const stats = [
    {
      name: 'Requests This Month',
      value: subscription?.monthly_requests_used || 0,
      limit: subscription?.monthly_request_limit || getRequestLimit(),
      icon: ChartBarIcon,
      color: 'text-blue-600',
    },
    {
      name: 'Credit Balance',
      value: credits ? formatCurrency(credits.balance_dollars) : '$0.00',
      icon: CreditCardIcon,
      color: 'text-green-600',
    },
    {
      name: 'Total Requests (7 days)',
      value: usage?.total_requests || 0,
      icon: ClockIcon,
      color: 'text-purple-600',
    },
  ]

  if (subscriptionLoading || creditsLoading || usageLoading) {
    return <LoadingSpinner text="Loading dashboard..." />
  }

  const requestsUsedPercentage = subscription 
    ? (subscription.monthly_requests_used / subscription.monthly_request_limit) * 100
    : 0

  const isNearLimit = requestsUsedPercentage > 80

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.full_name}!
            </h1>
            <p className="mt-1 text-sm text-gray-600">
              Here's what's happening with your AI workspace today.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium capitalize ${
              tier === 'free' && 'bg-gray-100 text-gray-800'
            } ${
              tier === 'basic' && 'bg-blue-100 text-blue-800'
            } ${
              tier === 'pro' && 'bg-purple-100 text-purple-800'
            } ${
              tier === 'enterprise' && 'bg-yellow-100 text-yellow-800'
            }`}>
              {tier} Plan
            </span>
          </div>
        </div>
      </div>

      {/* Usage Warning */}
      {isNearLimit && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                Approaching Usage Limit
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>
                  You've used {subscription?.monthly_requests_used} of {subscription?.monthly_request_limit} requests this month ({requestsUsedPercentage.toFixed(1)}%).
                  {tier === 'free' && (
                    <span className="ml-1">
                      <Link to="/billing" className="font-medium underline">
                        Upgrade your plan
                      </Link> for more requests.
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {stats.map((stat) => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                        {'limit' in stat && stat.limit && (
                          <span className="text-sm font-normal text-gray-500">
                            /{stat.limit}
                          </span>
                        )}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
              {'limit' in stat && stat.limit && (
                <div className="mt-3">
                  <div className="bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        (stat.value as number) / stat.limit > 0.8 
                          ? 'bg-red-500' 
                          : (stat.value as number) / stat.limit > 0.6 
                          ? 'bg-yellow-500' 
                          : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(((stat.value as number) / stat.limit) * 100, 100)}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
          <p className="mt-1 text-sm text-gray-600">
            Get started with AI generation tools
          </p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action) => (
              <div key={action.name} className="relative">
                <Link
                  to={action.href}
                  className={`block p-4 rounded-lg border-2 border-gray-200 hover:border-primary-300 transition-colors ${
                    !action.available ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'
                  }`}
                  onClick={(e) => !action.available && e.preventDefault()}
                >
                  <div className="flex items-center">
                    <div className={`flex-shrink-0 p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4 flex-1">
                      <h3 className="text-sm font-medium text-gray-900">
                        {action.name}
                      </h3>
                      <p className="mt-1 text-xs text-gray-500">
                        {action.description}
                      </p>
                    </div>
                    {action.available && (
                      <ArrowRightIcon className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </Link>
                {!action.available && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg">
                    <Link
                      to="/billing"
                      className="text-xs font-medium text-primary-600 hover:text-primary-500"
                    >
                      Upgrade to unlock
                    </Link>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      {usage && usage.daily_usage && usage.daily_usage.length > 0 && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
            <p className="mt-1 text-sm text-gray-600">
              Your AI usage over the last 7 days
            </p>
          </div>
          <div className="p-6">
            <div className="space-y-3">
              {usage.daily_usage.slice(0, 5).map((day) => (
                <div key={day.date} className="flex items-center justify-between py-2">
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {formatDate(day.date)}
                    </p>
                    <p className="text-xs text-gray-500">
                      {day.requests} requests
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatCurrency(day.cost_cents / 100)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 pt-4 border-t border-gray-200">
              <Link
                to="/billing"
                className="text-sm font-medium text-primary-600 hover:text-primary-500 flex items-center"
              >
                View detailed usage report
                <ArrowRightIcon className="ml-1 h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
