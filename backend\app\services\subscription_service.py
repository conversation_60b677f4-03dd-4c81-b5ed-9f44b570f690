"""
EliteForge AI - Subscription Management Service
Automatic billing cycles, tier management, and usage tracking
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import and_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.config import settings
from app.core.exceptions import CustomException
from app.db.models import (
    AIRequest, Payment, Subscription, SubscriptionStatus, SubscriptionTier, 
    User, UsageRecord, RequestType
)
from app.services.payoneer_service import PayoneerService

logger = logging.getLogger(__name__)


class SubscriptionManager:
    """Comprehensive subscription management system"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.payoneer_service = PayoneerService(db)
        
        # Tier configurations
        self.tier_configs = {
            SubscriptionTier.FREE: {
                "monthly_requests": 100,
                "daily_requests": 10,
                "max_concurrent": 1,
                "features": ["basic_text", "basic_image"],
                "models": ["llama-2-7b", "sd-1.5"],
                "priority": 1,
                "support_level": "community"
            },
            SubscriptionTier.BASIC: {
                "monthly_requests": 1000,
                "daily_requests": 50,
                "max_concurrent": 2,
                "features": ["text", "image", "code", "basic_enhancement"],
                "models": ["llama-2-7b", "llama-2-13b", "sd-1.5", "sd-2.1", "code-llama-7b"],
                "priority": 2,
                "support_level": "email"
            },
            SubscriptionTier.PRO: {
                "monthly_requests": 10000,
                "daily_requests": 500,
                "max_concurrent": 5,
                "features": ["all_text", "all_image", "all_code", "advanced_enhancement", "custom_algorithms"],
                "models": ["all_llama", "all_sd", "flux-schnell"],
                "priority": 3,
                "support_level": "priority"
            },
            SubscriptionTier.ENTERPRISE: {
                "monthly_requests": -1,  # Unlimited
                "daily_requests": -1,    # Unlimited
                "max_concurrent": 10,
                "features": ["everything", "custom_models", "api_access", "white_label"],
                "models": ["all_models", "custom_models"],
                "priority": 4,
                "support_level": "dedicated"
            }
        }
    
    async def get_user_subscription(self, user_id: UUID) -> Optional[Subscription]:
        """Get active subscription for user"""
        try:
            result = await self.db.execute(
                select(Subscription)
                .where(
                    and_(
                        Subscription.user_id == user_id,
                        Subscription.status.in_([SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING])
                    )
                )
                .options(selectinload(Subscription.user))
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Failed to get user subscription: {str(e)}")
            return None
    
    async def create_subscription(
        self,
        user_id: UUID,
        tier: SubscriptionTier,
        payment_method_id: str = None,
        trial_days: int = 7
    ) -> Subscription:
        """Create new subscription"""
        try:
            # Check if user already has active subscription
            existing = await self.get_user_subscription(user_id)
            if existing:
                raise CustomException(
                    status_code=400,
                    error_code="SUBSCRIPTION_EXISTS",
                    message="User already has an active subscription"
                )
            
            # Create subscription with Payoneer
            payoneer_result = await self.payoneer_service.create_subscription(
                user_id, tier, payment_method_id
            )
            
            # Get the created subscription
            subscription = await self.get_user_subscription(user_id)
            if not subscription:
                raise CustomException(
                    status_code=500,
                    error_code="SUBSCRIPTION_CREATION_FAILED",
                    message="Failed to create subscription"
                )
            
            # Initialize usage tracking
            await self._initialize_usage_tracking(subscription)
            
            logger.info(f"Created subscription {subscription.id} for user {user_id} with tier {tier.value}")
            
            return subscription
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Subscription creation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="SUBSCRIPTION_CREATION_ERROR",
                message=f"Failed to create subscription: {str(e)}"
            )
    
    async def upgrade_subscription(
        self,
        subscription_id: UUID,
        new_tier: SubscriptionTier
    ) -> Subscription:
        """Upgrade/downgrade subscription"""
        try:
            subscription = await self._get_subscription_by_id(subscription_id)
            if not subscription:
                raise CustomException(
                    status_code=404,
                    error_code="SUBSCRIPTION_NOT_FOUND",
                    message="Subscription not found"
                )
            
            old_tier = subscription.tier
            
            # Process upgrade with Payoneer
            await self.payoneer_service.upgrade_subscription(subscription_id, new_tier)
            
            # Update local subscription
            subscription.tier = new_tier
            subscription.monthly_request_limit = self.tier_configs[new_tier]["monthly_requests"]
            subscription.updated_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(subscription)
            
            # Log tier change
            await self._log_tier_change(subscription, old_tier, new_tier)
            
            logger.info(f"Upgraded subscription {subscription_id} from {old_tier.value} to {new_tier.value}")
            
            return subscription
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Subscription upgrade failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="SUBSCRIPTION_UPGRADE_ERROR",
                message=f"Failed to upgrade subscription: {str(e)}"
            )
    
    async def cancel_subscription(
        self,
        subscription_id: UUID,
        cancel_immediately: bool = False
    ) -> Subscription:
        """Cancel subscription"""
        try:
            subscription = await self._get_subscription_by_id(subscription_id)
            if not subscription:
                raise CustomException(
                    status_code=404,
                    error_code="SUBSCRIPTION_NOT_FOUND",
                    message="Subscription not found"
                )
            
            # Cancel with Payoneer
            await self.payoneer_service.cancel_subscription(subscription_id)
            
            # Update local subscription
            if cancel_immediately:
                subscription.status = SubscriptionStatus.CANCELLED
                subscription.cancelled_at = datetime.utcnow()
                subscription.current_period_end = datetime.utcnow()
            else:
                subscription.cancel_at_period_end = True
                subscription.cancelled_at = datetime.utcnow()
            
            await self.db.commit()
            await self.db.refresh(subscription)
            
            logger.info(f"Cancelled subscription {subscription_id}")
            
            return subscription
            
        except CustomException:
            raise
        except Exception as e:
            logger.error(f"Subscription cancellation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="SUBSCRIPTION_CANCELLATION_ERROR",
                message=f"Failed to cancel subscription: {str(e)}"
            )
    
    async def check_usage_limits(
        self,
        user_id: UUID,
        request_type: RequestType
    ) -> Dict[str, Any]:
        """Check if user can make request based on usage limits"""
        try:
            subscription = await self.get_user_subscription(user_id)
            if not subscription:
                # Free tier limits for users without subscription
                return await self._check_free_tier_limits(user_id, request_type)
            
            tier_config = self.tier_configs[subscription.tier]
            
            # Check monthly limits
            if tier_config["monthly_requests"] != -1:  # -1 means unlimited
                if subscription.monthly_requests_used >= tier_config["monthly_requests"]:
                    return {
                        "allowed": False,
                        "reason": "monthly_limit_exceeded",
                        "limit": tier_config["monthly_requests"],
                        "used": subscription.monthly_requests_used,
                        "reset_date": subscription.current_period_end.isoformat()
                    }
            
            # Check daily limits
            daily_usage = await self._get_daily_usage(user_id)
            if tier_config["daily_requests"] != -1:
                if daily_usage >= tier_config["daily_requests"]:
                    return {
                        "allowed": False,
                        "reason": "daily_limit_exceeded",
                        "limit": tier_config["daily_requests"],
                        "used": daily_usage,
                        "reset_date": (datetime.utcnow() + timedelta(days=1)).replace(hour=0, minute=0, second=0).isoformat()
                    }
            
            # Check concurrent requests
            concurrent_requests = await self._get_concurrent_requests(user_id)
            if concurrent_requests >= tier_config["max_concurrent"]:
                return {
                    "allowed": False,
                    "reason": "concurrent_limit_exceeded",
                    "limit": tier_config["max_concurrent"],
                    "current": concurrent_requests
                }
            
            return {
                "allowed": True,
                "tier": subscription.tier.value,
                "monthly_remaining": tier_config["monthly_requests"] - subscription.monthly_requests_used if tier_config["monthly_requests"] != -1 else -1,
                "daily_remaining": tier_config["daily_requests"] - daily_usage if tier_config["daily_requests"] != -1 else -1
            }
            
        except Exception as e:
            logger.error(f"Usage limit check failed: {str(e)}")
            return {"allowed": False, "reason": "check_failed", "error": str(e)}
    
    async def record_usage(
        self,
        user_id: UUID,
        request_type: RequestType,
        tokens_used: int = 0,
        cost_cents: int = 0,
        metadata: Dict[str, Any] = None
    ) -> UsageRecord:
        """Record usage for billing and analytics"""
        try:
            # Create usage record
            usage_record = UsageRecord(
                user_id=user_id,
                request_type=request_type,
                tokens_used=tokens_used,
                cost_cents=cost_cents,
                metadata=metadata or {},
                created_at=datetime.utcnow()
            )
            
            self.db.add(usage_record)
            
            # Update subscription usage counter
            subscription = await self.get_user_subscription(user_id)
            if subscription:
                subscription.monthly_requests_used += 1
                subscription.total_requests_used += 1
            
            await self.db.commit()
            await self.db.refresh(usage_record)
            
            return usage_record
            
        except Exception as e:
            logger.error(f"Usage recording failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="USAGE_RECORDING_FAILED",
                message=f"Failed to record usage: {str(e)}"
            )
    
    async def process_billing_cycle(self) -> Dict[str, Any]:
        """Process monthly billing cycle for all subscriptions"""
        try:
            logger.info("Starting billing cycle processing")
            
            # Get all active subscriptions due for billing
            current_time = datetime.utcnow()
            result = await self.db.execute(
                select(Subscription)
                .where(
                    and_(
                        Subscription.status == SubscriptionStatus.ACTIVE,
                        Subscription.current_period_end <= current_time
                    )
                )
            )
            
            subscriptions = result.scalars().all()
            
            processed = 0
            failed = 0
            
            for subscription in subscriptions:
                try:
                    await self._process_subscription_renewal(subscription)
                    processed += 1
                except Exception as e:
                    logger.error(f"Failed to process subscription {subscription.id}: {str(e)}")
                    failed += 1
            
            logger.info(f"Billing cycle completed: {processed} processed, {failed} failed")
            
            return {
                "processed": processed,
                "failed": failed,
                "total": len(subscriptions)
            }
            
        except Exception as e:
            logger.error(f"Billing cycle processing failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="BILLING_CYCLE_FAILED",
                message=f"Billing cycle processing failed: {str(e)}"
            )
    
    async def get_subscription_analytics(self, user_id: UUID) -> Dict[str, Any]:
        """Get subscription analytics for user"""
        try:
            subscription = await self.get_user_subscription(user_id)
            if not subscription:
                return {"error": "No active subscription"}
            
            # Get usage statistics
            usage_stats = await self._get_usage_statistics(user_id)
            
            # Get tier information
            tier_config = self.tier_configs[subscription.tier]
            
            return {
                "subscription": {
                    "id": str(subscription.id),
                    "tier": subscription.tier.value,
                    "status": subscription.status.value,
                    "created_at": subscription.created_at.isoformat(),
                    "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                    "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
                    "cancel_at_period_end": subscription.cancel_at_period_end
                },
                "usage": {
                    "monthly_used": subscription.monthly_requests_used,
                    "monthly_limit": tier_config["monthly_requests"],
                    "total_used": subscription.total_requests_used,
                    "daily_stats": usage_stats["daily"],
                    "request_type_breakdown": usage_stats["by_type"]
                },
                "tier_features": {
                    "features": tier_config["features"],
                    "models": tier_config["models"],
                    "max_concurrent": tier_config["max_concurrent"],
                    "support_level": tier_config["support_level"],
                    "priority": tier_config["priority"]
                }
            }
            
        except Exception as e:
            logger.error(f"Analytics retrieval failed: {str(e)}")
            return {"error": str(e)}
    
    async def _get_subscription_by_id(self, subscription_id: UUID) -> Optional[Subscription]:
        """Get subscription by ID"""
        result = await self.db.execute(
            select(Subscription).where(Subscription.id == subscription_id)
        )
        return result.scalar_one_or_none()
    
    async def _initialize_usage_tracking(self, subscription: Subscription):
        """Initialize usage tracking for new subscription"""
        subscription.monthly_requests_used = 0
        subscription.total_requests_used = 0
        subscription.current_period_start = datetime.utcnow()
        subscription.current_period_end = datetime.utcnow() + timedelta(days=30)
        
        await self.db.commit()
    
    async def _check_free_tier_limits(self, user_id: UUID, request_type: RequestType) -> Dict[str, Any]:
        """Check limits for free tier users"""
        free_config = self.tier_configs[SubscriptionTier.FREE]
        
        # Check daily usage
        daily_usage = await self._get_daily_usage(user_id)
        if daily_usage >= free_config["daily_requests"]:
            return {
                "allowed": False,
                "reason": "free_daily_limit_exceeded",
                "limit": free_config["daily_requests"],
                "used": daily_usage,
                "upgrade_required": True
            }
        
        return {
            "allowed": True,
            "tier": "free",
            "daily_remaining": free_config["daily_requests"] - daily_usage,
            "upgrade_available": True
        }
    
    async def _get_daily_usage(self, user_id: UUID) -> int:
        """Get daily usage count for user"""
        today = datetime.utcnow().date()
        result = await self.db.execute(
            select(UsageRecord)
            .where(
                and_(
                    UsageRecord.user_id == user_id,
                    UsageRecord.created_at >= today
                )
            )
        )
        return len(result.scalars().all())
    
    async def _get_concurrent_requests(self, user_id: UUID) -> int:
        """Get current concurrent requests for user"""
        # This would typically check active AI requests
        result = await self.db.execute(
            select(AIRequest)
            .where(
                and_(
                    AIRequest.user_id == user_id,
                    AIRequest.status == "processing"
                )
            )
        )
        return len(result.scalars().all())
    
    async def _process_subscription_renewal(self, subscription: Subscription):
        """Process individual subscription renewal"""
        # Reset usage counters
        subscription.monthly_requests_used = 0
        subscription.current_period_start = subscription.current_period_end
        subscription.current_period_end = subscription.current_period_end + timedelta(days=30)
        
        # Check if subscription should be cancelled
        if subscription.cancel_at_period_end:
            subscription.status = SubscriptionStatus.CANCELLED
            subscription.cancelled_at = datetime.utcnow()
        
        await self.db.commit()
    
    async def _log_tier_change(self, subscription: Subscription, old_tier: SubscriptionTier, new_tier: SubscriptionTier):
        """Log subscription tier change"""
        logger.info(f"Subscription {subscription.id} tier changed: {old_tier.value} -> {new_tier.value}")
        
        # Could store in audit log table
        # audit_log = AuditLog(
        #     user_id=subscription.user_id,
        #     action="tier_change",
        #     details={"old_tier": old_tier.value, "new_tier": new_tier.value}
        # )
        # self.db.add(audit_log)
    
    async def _get_usage_statistics(self, user_id: UUID) -> Dict[str, Any]:
        """Get detailed usage statistics"""
        # Get last 30 days of usage
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        
        result = await self.db.execute(
            select(UsageRecord)
            .where(
                and_(
                    UsageRecord.user_id == user_id,
                    UsageRecord.created_at >= thirty_days_ago
                )
            )
        )
        
        usage_records = result.scalars().all()
        
        # Group by day
        daily_stats = {}
        type_breakdown = {}
        
        for record in usage_records:
            day = record.created_at.date().isoformat()
            request_type = record.request_type.value
            
            daily_stats[day] = daily_stats.get(day, 0) + 1
            type_breakdown[request_type] = type_breakdown.get(request_type, 0) + 1
        
        return {
            "daily": daily_stats,
            "by_type": type_breakdown
        }


__all__ = ["SubscriptionManager"]
