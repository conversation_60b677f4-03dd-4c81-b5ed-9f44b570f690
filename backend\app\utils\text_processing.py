"""
EliteForge AI - Text Processing Utilities
Custom algorithms for text enhancement, code formatting, and content optimization
"""

import re
import logging
from typing import Any, Dict, List, Optional, Tuple
import json

from app.core.config import settings

logger = logging.getLogger(__name__)


class TextProcessor:
    """Advanced text processing and enhancement"""
    
    def __init__(self):
        # Common code patterns for detection and formatting
        self.code_patterns = {
            "python": r"```python\n(.*?)\n```",
            "javascript": r"```javascript\n(.*?)\n```",
            "typescript": r"```typescript\n(.*?)\n```",
            "html": r"```html\n(.*?)\n```",
            "css": r"```css\n(.*?)\n```",
            "sql": r"```sql\n(.*?)\n```",
            "bash": r"```bash\n(.*?)\n```",
            "json": r"```json\n(.*?)\n```",
        }
        
        # Text quality metrics
        self.quality_indicators = {
            "coherence": ["therefore", "however", "moreover", "furthermore", "consequently"],
            "clarity": ["specifically", "for example", "in other words", "that is"],
            "completeness": ["in conclusion", "to summarize", "overall", "finally"],
        }
    
    async def clean_generated_text(self, text: str) -> str:
        """Clean and enhance generated text"""
        try:
            # Remove common generation artifacts
            cleaned = self._remove_generation_artifacts(text)
            
            # Fix common formatting issues
            cleaned = self._fix_formatting_issues(cleaned)
            
            # Enhance readability
            cleaned = await self._enhance_readability(cleaned)
            
            # Remove excessive whitespace
            cleaned = self._normalize_whitespace(cleaned)
            
            return cleaned.strip()
            
        except Exception as e:
            logger.error(f"Text cleaning failed: {str(e)}")
            return text
    
    async def format_code_output(self, code_text: str, language: str = "python") -> str:
        """Format and enhance generated code"""
        try:
            # Extract code from markdown blocks if present
            code = self._extract_code_from_markdown(code_text, language)
            
            # Clean up code formatting
            code = self._clean_code_formatting(code)
            
            # Add proper indentation
            code = self._fix_code_indentation(code, language)
            
            # Add comments and documentation
            code = await self._enhance_code_documentation(code, language)
            
            # Validate syntax if possible
            code = await self._validate_code_syntax(code, language)
            
            return code
            
        except Exception as e:
            logger.error(f"Code formatting failed: {str(e)}")
            return code_text
    
    async def analyze_text_quality(self, text: str) -> Dict[str, Any]:
        """Analyze text quality and provide metrics"""
        try:
            metrics = {
                "word_count": len(text.split()),
                "sentence_count": len(re.findall(r'[.!?]+', text)),
                "paragraph_count": len([p for p in text.split('\n\n') if p.strip()]),
                "avg_sentence_length": 0,
                "readability_score": 0,
                "coherence_score": 0,
                "clarity_score": 0,
                "completeness_score": 0,
            }
            
            # Calculate average sentence length
            sentences = re.findall(r'[^.!?]*[.!?]', text)
            if sentences:
                total_words = sum(len(sentence.split()) for sentence in sentences)
                metrics["avg_sentence_length"] = total_words / len(sentences)
            
            # Calculate quality scores
            metrics["coherence_score"] = self._calculate_coherence_score(text)
            metrics["clarity_score"] = self._calculate_clarity_score(text)
            metrics["completeness_score"] = self._calculate_completeness_score(text)
            
            # Overall readability score
            metrics["readability_score"] = (
                metrics["coherence_score"] + 
                metrics["clarity_score"] + 
                metrics["completeness_score"]
            ) / 3
            
            return metrics
            
        except Exception as e:
            logger.error(f"Text quality analysis failed: {str(e)}")
            return {"error": str(e)}
    
    async def enhance_text_structure(self, text: str) -> str:
        """Enhance text structure and organization"""
        try:
            # Split into paragraphs
            paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
            
            # Enhance each paragraph
            enhanced_paragraphs = []
            for paragraph in paragraphs:
                enhanced = await self._enhance_paragraph_structure(paragraph)
                enhanced_paragraphs.append(enhanced)
            
            # Add transitions between paragraphs if needed
            structured_text = await self._add_paragraph_transitions(enhanced_paragraphs)
            
            return structured_text
            
        except Exception as e:
            logger.error(f"Text structure enhancement failed: {str(e)}")
            return text
    
    async def extract_key_information(self, text: str) -> Dict[str, List[str]]:
        """Extract key information from text"""
        try:
            info = {
                "key_points": [],
                "entities": [],
                "topics": [],
                "action_items": [],
                "questions": [],
            }
            
            # Extract key points (sentences with certain indicators)
            key_point_indicators = ["important", "key", "main", "primary", "essential", "crucial"]
            sentences = re.split(r'[.!?]+', text)
            
            for sentence in sentences:
                sentence = sentence.strip()
                if any(indicator in sentence.lower() for indicator in key_point_indicators):
                    info["key_points"].append(sentence)
            
            # Extract questions
            questions = re.findall(r'[^.!?]*\?', text)
            info["questions"] = [q.strip() + '?' for q in questions if q.strip()]
            
            # Extract potential action items
            action_indicators = ["should", "must", "need to", "have to", "will", "plan to"]
            for sentence in sentences:
                sentence = sentence.strip()
                if any(indicator in sentence.lower() for indicator in action_indicators):
                    info["action_items"].append(sentence)
            
            # Simple topic extraction (most frequent meaningful words)
            words = re.findall(r'\b[a-zA-Z]{4,}\b', text.lower())
            word_freq = {}
            for word in words:
                if word not in ["this", "that", "with", "from", "they", "have", "been", "will"]:
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # Get top topics
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            info["topics"] = [word for word, freq in sorted_words[:10] if freq > 1]
            
            return info
            
        except Exception as e:
            logger.error(f"Key information extraction failed: {str(e)}")
            return {"error": str(e)}
    
    def _remove_generation_artifacts(self, text: str) -> str:
        """Remove common AI generation artifacts"""
        # Remove repetitive patterns
        text = re.sub(r'(.{10,}?)\1{2,}', r'\1', text)
        
        # Remove incomplete sentences at the end
        sentences = text.split('.')
        if len(sentences) > 1 and len(sentences[-1].strip()) < 10:
            text = '.'.join(sentences[:-1]) + '.'
        
        # Remove common AI disclaimers
        disclaimers = [
            "As an AI", "I'm an AI", "As a language model", "I cannot", "I don't have",
            "I'm not able to", "I can't provide", "I apologize, but"
        ]
        
        for disclaimer in disclaimers:
            text = re.sub(rf'\b{re.escape(disclaimer)}[^.]*\.', '', text, flags=re.IGNORECASE)
        
        return text
    
    def _fix_formatting_issues(self, text: str) -> str:
        """Fix common formatting issues"""
        # Fix spacing around punctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        text = re.sub(r'([.!?])\s*([A-Z])', r'\1 \2', text)
        
        # Fix quotation marks
        text = re.sub(r'\s+"([^"]*)"', r' "\1"', text)
        
        # Fix apostrophes
        text = re.sub(r"(\w)\s+'(\w)", r"\1'\2", text)
        
        return text
    
    async def _enhance_readability(self, text: str) -> str:
        """Enhance text readability"""
        # Break up very long sentences
        sentences = re.split(r'([.!?]+)', text)
        enhanced_sentences = []
        
        for i in range(0, len(sentences), 2):
            if i + 1 < len(sentences):
                sentence = sentences[i]
                punctuation = sentences[i + 1]
                
                # If sentence is very long, try to break it up
                if len(sentence.split()) > 25:
                    # Look for natural break points
                    break_points = [", and ", ", but ", ", however ", ", therefore "]
                    for break_point in break_points:
                        if break_point in sentence:
                            parts = sentence.split(break_point, 1)
                            sentence = parts[0] + ". " + parts[1].strip().capitalize()
                            break
                
                enhanced_sentences.append(sentence + punctuation)
        
        return ''.join(enhanced_sentences)
    
    def _normalize_whitespace(self, text: str) -> str:
        """Normalize whitespace in text"""
        # Replace multiple spaces with single space
        text = re.sub(r' +', ' ', text)
        
        # Replace multiple newlines with double newline
        text = re.sub(r'\n{3,}', '\n\n', text)
        
        # Remove trailing whitespace from lines
        lines = text.split('\n')
        lines = [line.rstrip() for line in lines]
        
        return '\n'.join(lines)
    
    def _extract_code_from_markdown(self, text: str, language: str) -> str:
        """Extract code from markdown code blocks"""
        # Try to find code block for specific language
        pattern = self.code_patterns.get(language, r'```\w*\n(.*?)\n```')
        matches = re.findall(pattern, text, re.DOTALL)
        
        if matches:
            return matches[0]
        
        # If no specific language block found, try generic code block
        generic_pattern = r'```\n?(.*?)\n?```'
        matches = re.findall(generic_pattern, text, re.DOTALL)
        
        if matches:
            return matches[0]
        
        # If no code blocks found, return original text
        return text
    
    def _clean_code_formatting(self, code: str) -> str:
        """Clean up code formatting"""
        # Remove extra blank lines
        lines = code.split('\n')
        cleaned_lines = []
        prev_blank = False
        
        for line in lines:
            is_blank = not line.strip()
            if not (is_blank and prev_blank):
                cleaned_lines.append(line)
            prev_blank = is_blank
        
        return '\n'.join(cleaned_lines)
    
    def _fix_code_indentation(self, code: str, language: str) -> str:
        """Fix code indentation"""
        lines = code.split('\n')
        if not lines:
            return code
        
        # Determine indentation style (spaces vs tabs)
        indent_char = '    '  # Default to 4 spaces
        
        # Find the minimum indentation level
        min_indent = float('inf')
        for line in lines:
            if line.strip():
                indent_level = len(line) - len(line.lstrip())
                min_indent = min(min_indent, indent_level)
        
        # Remove common leading whitespace
        if min_indent > 0 and min_indent != float('inf'):
            lines = [line[min_indent:] if line.strip() else line for line in lines]
        
        return '\n'.join(lines)
    
    async def _enhance_code_documentation(self, code: str, language: str) -> str:
        """Add comments and documentation to code"""
        # This is a simplified version - in practice, you'd use AST parsing
        lines = code.split('\n')
        enhanced_lines = []
        
        for line in lines:
            enhanced_lines.append(line)
            
            # Add comments for function definitions
            if language == "python" and line.strip().startswith('def '):
                # Extract function name
                func_match = re.match(r'\s*def\s+(\w+)', line)
                if func_match:
                    func_name = func_match.group(1)
                    indent = len(line) - len(line.lstrip())
                    comment = ' ' * (indent + 4) + f'"""Function: {func_name}"""'
                    enhanced_lines.append(comment)
        
        return '\n'.join(enhanced_lines)
    
    async def _validate_code_syntax(self, code: str, language: str) -> str:
        """Validate code syntax and fix common issues"""
        if language == "python":
            try:
                # Try to compile the code to check for syntax errors
                compile(code, '<string>', 'exec')
            except SyntaxError as e:
                logger.warning(f"Python syntax error in generated code: {str(e)}")
                # Could implement basic syntax fixing here
        
        return code
    
    def _calculate_coherence_score(self, text: str) -> float:
        """Calculate text coherence score"""
        indicators = self.quality_indicators["coherence"]
        score = 0
        
        for indicator in indicators:
            if indicator in text.lower():
                score += 1
        
        # Normalize to 0-100 scale
        return min(100, (score / len(indicators)) * 100)
    
    def _calculate_clarity_score(self, text: str) -> float:
        """Calculate text clarity score"""
        indicators = self.quality_indicators["clarity"]
        score = 0
        
        for indicator in indicators:
            if indicator in text.lower():
                score += 1
        
        # Also consider sentence length (shorter sentences are clearer)
        sentences = re.findall(r'[^.!?]*[.!?]', text)
        if sentences:
            avg_length = sum(len(s.split()) for s in sentences) / len(sentences)
            length_score = max(0, 100 - (avg_length - 15) * 2)  # Penalty for very long sentences
        else:
            length_score = 50
        
        indicator_score = min(100, (score / len(indicators)) * 100)
        return (indicator_score + length_score) / 2
    
    def _calculate_completeness_score(self, text: str) -> float:
        """Calculate text completeness score"""
        indicators = self.quality_indicators["completeness"]
        score = 0
        
        for indicator in indicators:
            if indicator in text.lower():
                score += 1
        
        # Check if text ends properly
        if text.strip().endswith(('.', '!', '?')):
            score += 1
        
        # Normalize to 0-100 scale
        return min(100, (score / (len(indicators) + 1)) * 100)
    
    async def _enhance_paragraph_structure(self, paragraph: str) -> str:
        """Enhance individual paragraph structure"""
        sentences = re.split(r'([.!?]+)', paragraph)
        if len(sentences) < 3:
            return paragraph
        
        # Ensure first sentence is strong
        first_sentence = sentences[0].strip()
        if len(first_sentence.split()) < 5:
            # First sentence might be too short, consider combining with next
            if len(sentences) >= 4:
                first_sentence = first_sentence + sentences[1] + " " + sentences[2].strip()
                sentences = [first_sentence] + sentences[3:]
        
        return ''.join(sentences)
    
    async def _add_paragraph_transitions(self, paragraphs: List[str]) -> str:
        """Add transitions between paragraphs"""
        if len(paragraphs) <= 1:
            return '\n\n'.join(paragraphs)
        
        transitions = [
            "Furthermore, ", "Additionally, ", "Moreover, ", "In addition, ",
            "However, ", "Nevertheless, ", "On the other hand, ",
            "Consequently, ", "Therefore, ", "As a result, "
        ]
        
        enhanced_paragraphs = [paragraphs[0]]  # First paragraph unchanged
        
        for i, paragraph in enumerate(paragraphs[1:], 1):
            # Simple heuristic for choosing transitions
            if i < len(paragraphs) - 1:
                # Use additive transitions for middle paragraphs
                transition = transitions[i % 4]
            else:
                # Use conclusive transitions for last paragraph
                transition = transitions[-3 + (i % 3)]
            
            # Only add transition if paragraph doesn't already start with one
            if not any(paragraph.startswith(t.strip()) for t in transitions):
                paragraph = transition + paragraph.lower()[0] + paragraph[1:]
            
            enhanced_paragraphs.append(paragraph)
        
        return '\n\n'.join(enhanced_paragraphs)


# Global processor instance
text_processor = TextProcessor()

# Convenience functions
async def clean_generated_text(text: str) -> str:
    """Clean and enhance generated text"""
    return await text_processor.clean_generated_text(text)

async def format_code_output(code_text: str, language: str = "python") -> str:
    """Format and enhance generated code"""
    return await text_processor.format_code_output(code_text, language)

async def analyze_text_quality(text: str) -> Dict[str, Any]:
    """Analyze text quality and provide metrics"""
    return await text_processor.analyze_text_quality(text)

async def enhance_text_structure(text: str) -> str:
    """Enhance text structure and organization"""
    return await text_processor.enhance_text_structure(text)

async def extract_key_information(text: str) -> Dict[str, List[str]]:
    """Extract key information from text"""
    return await text_processor.extract_key_information(text)

__all__ = [
    "TextProcessor",
    "text_processor", 
    "clean_generated_text",
    "format_code_output",
    "analyze_text_quality",
    "enhance_text_structure",
    "extract_key_information"
]
