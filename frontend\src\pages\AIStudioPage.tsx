import React, { useState, useEffect } from 'react'
import { useQuery } from '@tanstack/react-query'
import {
  CpuChipIcon,
  PhotoIcon,
  CodeBracketIcon,
  ChatBubbleLeftRightIcon,
  SparklesIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { aiApi, billingApi } from '../lib/api'
import { useAuth } from '../lib/auth'
import TextGenerationPanel from '../components/ai/TextGenerationPanel'
import ImageGenerationPanel from '../components/ai/ImageGenerationPanel'
import CodeGenerationPanel from '../components/ai/CodeGenerationPanel'
import ChatPanel from '../components/ai/ChatPanel'
import ModelSelector from '../components/ai/ModelSelector'
import UsageMeter from '../components/ai/UsageMeter'

type GenerationType = 'text' | 'image' | 'code' | 'chat'

const generationTypes = [
  {
    id: 'text' as GenerationType,
    name: 'Text Generation',
    description: 'Generate articles, stories, and creative content',
    icon: SparklesIcon,
    color: 'bg-blue-500',
  },
  {
    id: 'image' as GenerationType,
    name: 'Image Generation',
    description: 'Create stunning images from text descriptions',
    icon: PhotoIcon,
    color: 'bg-purple-500',
  },
  {
    id: 'code' as GenerationType,
    name: 'Code Generation',
    description: 'Generate code snippets and programming solutions',
    icon: CodeBracketIcon,
    color: 'bg-green-500',
  },
  {
    id: 'chat' as GenerationType,
    name: 'AI Chat',
    description: 'Interactive conversations with AI models',
    icon: ChatBubbleLeftRightIcon,
    color: 'bg-orange-500',
  },
]

export default function AIStudioPage() {
  const { user } = useAuth()
  const [activeType, setActiveType] = useState<GenerationType>('text')
  const [isGenerating, setIsGenerating] = useState(false)

  // Fetch available models
  const { data: models, isLoading: modelsLoading } = useQuery({
    queryKey: ['ai-models'],
    queryFn: () => aiApi.getModels(),
  })

  // Fetch current usage
  const { data: usageData, refetch: refetchUsage } = useQuery({
    queryKey: ['usage-limits'],
    queryFn: () => billingApi.checkUsageLimits(activeType),
    refetchInterval: 30000, // Refetch every 30 seconds
  })

  // Fetch subscription info
  const { data: subscriptionData } = useQuery({
    queryKey: ['subscription'],
    queryFn: () => billingApi.getCurrentSubscription(),
  })

  const subscription = subscriptionData?.data?.subscription
  const usage = usageData?.data

  const handleGenerationStart = () => {
    setIsGenerating(true)
  }

  const handleGenerationComplete = () => {
    setIsGenerating(false)
    refetchUsage()
  }

  const handleGenerationError = () => {
    setIsGenerating(false)
  }

  const renderGenerationPanel = () => {
    const commonProps = {
      onGenerationStart: handleGenerationStart,
      onGenerationComplete: handleGenerationComplete,
      onGenerationError: handleGenerationError,
      isGenerating,
      subscription,
      models: models?.data || [],
    }

    switch (activeType) {
      case 'text':
        return <TextGenerationPanel {...commonProps} />
      case 'image':
        return <ImageGenerationPanel {...commonProps} />
      case 'code':
        return <CodeGenerationPanel {...commonProps} />
      case 'chat':
        return <ChatPanel {...commonProps} />
      default:
        return <TextGenerationPanel {...commonProps} />
    }
  }

  const canUseFeature = (type: GenerationType) => {
    if (!subscription) return false

    const tierLimits = {
      free: ['text'],
      basic: ['text', 'code'],
      standard: ['text', 'code', 'image'],
      advanced: ['text', 'code', 'image', 'chat'],
      enterprise: ['text', 'code', 'image', 'chat'],
    }

    const userTier = subscription.tier.toLowerCase()
    return tierLimits[userTier as keyof typeof tierLimits]?.includes(type) || false
  }

  const isAtUsageLimit = () => {
    if (!usage || !subscription) return false
    if (subscription.tier === 'enterprise') return false

    return usage.requests_used >= usage.request_limit
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <CpuChipIcon className="h-8 w-8 text-primary-600 mr-3" />
              AI Studio
            </h1>
            <p className="mt-2 text-gray-600">
              Generate content using state-of-the-art AI models
            </p>
          </div>

          {/* Usage Meter */}
          <div className="flex items-center space-x-4">
            <UsageMeter
              usage={usage}
              subscription={subscription}
              className="w-64"
            />
            {isGenerating && (
              <div className="flex items-center text-primary-600">
                <ClockIcon className="h-5 w-5 animate-spin mr-2" />
                <span className="text-sm font-medium">Generating...</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Usage Limit Warning */}
      {isAtUsageLimit() && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 mr-3" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800">
                Usage Limit Reached
              </h3>
              <p className="text-sm text-yellow-700 mt-1">
                You've reached your monthly request limit.
                <a href="/billing" className="font-medium underline ml-1">
                  Upgrade your plan
                </a> to continue generating content.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Generation Type Selector */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Choose Generation Type
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {generationTypes.map((type) => {
            const canUse = canUseFeature(type.id)
            const isActive = activeType === type.id

            return (
              <button
                key={type.id}
                onClick={() => canUse && setActiveType(type.id)}
                disabled={!canUse || isGenerating}
                className={`
                  relative p-4 rounded-lg border-2 transition-all duration-200
                  ${isActive
                    ? 'border-primary-500 bg-primary-50'
                    : canUse
                      ? 'border-gray-200 hover:border-gray-300 bg-white'
                      : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-50'
                  }
                  ${isGenerating && !isActive ? 'opacity-50 cursor-not-allowed' : ''}
                `}
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${type.color} ${!canUse ? 'opacity-50' : ''}`}>
                    <type.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <h3 className={`font-medium ${isActive ? 'text-primary-900' : 'text-gray-900'}`}>
                      {type.name}
                    </h3>
                    <p className={`text-sm ${isActive ? 'text-primary-700' : 'text-gray-500'}`}>
                      {type.description}
                    </p>
                  </div>
                </div>

                {!canUse && (
                  <div className="absolute top-2 right-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Upgrade Required
                    </span>
                  </div>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Generation Panel */}
      <div className="bg-white shadow rounded-lg">
        {renderGenerationPanel()}
      </div>
    </div>
  )
}
