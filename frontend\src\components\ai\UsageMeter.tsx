import React from 'react'
import { ChartBarIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { formatNumber } from '../../lib/utils'

interface UsageMeterProps {
  usage?: {
    requests_used: number
    request_limit: number
    cost_used_cents?: number
    cost_limit_cents?: number
  }
  subscription?: {
    tier: string
    status: string
  }
  className?: string
}

export default function UsageMeter({ usage, subscription, className = '' }: UsageMeterProps) {
  if (!usage || !subscription) {
    return (
      <div className={`bg-gray-50 rounded-lg p-3 ${className}`}>
        <div className="flex items-center text-gray-500">
          <ChartBarIcon className="h-5 w-5 mr-2" />
          <span className="text-sm">Loading usage...</span>
        </div>
      </div>
    )
  }

  const requestsUsed = usage.requests_used || 0
  const requestLimit = usage.request_limit || 0
  const requestPercentage = requestLimit > 0 ? (requestsUsed / requestLimit) * 100 : 0
  
  const costUsed = usage.cost_used_cents || 0
  const costLimit = usage.cost_limit_cents || 0
  const costPercentage = costLimit > 0 ? (costUsed / costLimit) * 100 : 0

  const isNearLimit = requestPercentage >= 80
  const isAtLimit = requestPercentage >= 100

  const getProgressColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-red-500'
    if (percentage >= 80) return 'bg-yellow-500'
    return 'bg-primary-500'
  }

  const getTextColor = (percentage: number) => {
    if (percentage >= 100) return 'text-red-700'
    if (percentage >= 80) return 'text-yellow-700'
    return 'text-gray-700'
  }

  return (
    <div className={`bg-white border rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <ChartBarIcon className="h-5 w-5 text-gray-400 mr-2" />
          <span className="text-sm font-medium text-gray-900">Usage</span>
        </div>
        {isNearLimit && (
          <ExclamationTriangleIcon className={`h-4 w-4 ${isAtLimit ? 'text-red-500' : 'text-yellow-500'}`} />
        )}
      </div>

      {/* Requests Usage */}
      <div className="mb-3">
        <div className="flex items-center justify-between mb-1">
          <span className="text-xs text-gray-600">Requests</span>
          <span className={`text-xs font-medium ${getTextColor(requestPercentage)}`}>
            {formatNumber(requestsUsed)} / {subscription.tier === 'enterprise' ? '∞' : formatNumber(requestLimit)}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(requestPercentage)}`}
            style={{ width: `${Math.min(requestPercentage, 100)}%` }}
          />
        </div>
      </div>

      {/* Cost Usage (if applicable) */}
      {costLimit > 0 && (
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-gray-600">Credits</span>
            <span className={`text-xs font-medium ${getTextColor(costPercentage)}`}>
              ${(costUsed / 100).toFixed(2)} / ${(costLimit / 100).toFixed(2)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(costPercentage)}`}
              style={{ width: `${Math.min(costPercentage, 100)}%` }}
            />
          </div>
        </div>
      )}

      {/* Tier Badge */}
      <div className="mt-3 pt-3 border-t border-gray-100">
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          subscription.tier === 'enterprise' 
            ? 'bg-purple-100 text-purple-800'
            : subscription.tier === 'advanced'
            ? 'bg-blue-100 text-blue-800'
            : subscription.tier === 'standard'
            ? 'bg-green-100 text-green-800'
            : subscription.tier === 'basic'
            ? 'bg-yellow-100 text-yellow-800'
            : 'bg-gray-100 text-gray-800'
        }`}>
          {subscription.tier.charAt(0).toUpperCase() + subscription.tier.slice(1)} Plan
        </span>
      </div>
    </div>
  )
}
