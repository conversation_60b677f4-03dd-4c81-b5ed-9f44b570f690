"""
EliteForge AI - Database Configuration and Management
PostgreSQL database setup with SQLAlchemy and async support
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, text
from contextlib import asynccontextmanager
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# Create async engine
async_engine = create_async_engine(
    settings.DATABASE_URL,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    echo=settings.DEBUG,
    future=True
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Create sync engine for migrations
sync_engine = create_engine(
    settings.DATABASE_URL.replace("+asyncpg", ""),
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    echo=settings.DEBUG
)

# Create sync session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)

# Base class for models
Base = declarative_base()


async def init_db():
    """Initialize database and create tables"""
    try:
        async with async_engine.begin() as conn:
            # Import all models to ensure they are registered
            from app.db import models  # noqa
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
            
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        raise


async def check_db_health() -> bool:
    """Check database health"""
    try:
        async with AsyncSessionLocal() as session:
            result = await session.execute(text("SELECT 1"))
            return result.scalar() == 1
    except Exception as e:
        logger.error(f"Database health check failed: {str(e)}")
        return False


@asynccontextmanager
async def get_db_session():
    """Get database session with automatic cleanup"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def get_db():
    """Dependency to get database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


class DatabaseManager:
    """Database manager for advanced operations"""
    
    def __init__(self):
        self.engine = async_engine
        self.session_factory = AsyncSessionLocal
    
    async def execute_raw_query(self, query: str, params: dict = None):
        """Execute raw SQL query"""
        async with self.session_factory() as session:
            result = await session.execute(text(query), params or {})
            await session.commit()
            return result
    
    async def get_table_info(self, table_name: str):
        """Get table information"""
        query = """
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns
        WHERE table_name = :table_name
        ORDER BY ordinal_position;
        """
        async with self.session_factory() as session:
            result = await session.execute(text(query), {"table_name": table_name})
            return result.fetchall()
    
    async def get_database_stats(self):
        """Get database statistics"""
        queries = {
            "total_tables": "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'",
            "database_size": "SELECT pg_size_pretty(pg_database_size(current_database()))",
            "active_connections": "SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'",
            "total_connections": "SELECT COUNT(*) FROM pg_stat_activity"
        }
        
        stats = {}
        async with self.session_factory() as session:
            for key, query in queries.items():
                try:
                    result = await session.execute(text(query))
                    stats[key] = result.scalar()
                except Exception as e:
                    logger.error(f"Failed to get {key}: {str(e)}")
                    stats[key] = None
        
        return stats
    
    async def backup_table(self, table_name: str, backup_name: str = None):
        """Create a backup of a table"""
        if not backup_name:
            from datetime import datetime
            backup_name = f"{table_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        query = f"CREATE TABLE {backup_name} AS SELECT * FROM {table_name}"
        
        async with self.session_factory() as session:
            await session.execute(text(query))
            await session.commit()
            logger.info(f"Table {table_name} backed up as {backup_name}")
            return backup_name
    
    async def optimize_database(self):
        """Run database optimization tasks"""
        optimization_queries = [
            "VACUUM ANALYZE;",
            "REINDEX DATABASE eliteforge;",
        ]
        
        async with self.session_factory() as session:
            for query in optimization_queries:
                try:
                    await session.execute(text(query))
                    await session.commit()
                    logger.info(f"Executed optimization query: {query}")
                except Exception as e:
                    logger.error(f"Failed to execute {query}: {str(e)}")


# Create database manager instance
db_manager = DatabaseManager()


# Database event listeners
from sqlalchemy import event

@event.listens_for(async_engine.sync_engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set database connection parameters"""
    if "postgresql" in settings.DATABASE_URL:
        # Set PostgreSQL specific parameters
        with dbapi_connection.cursor() as cursor:
            cursor.execute("SET timezone TO 'UTC'")
            cursor.execute("SET statement_timeout = '30s'")


@event.listens_for(async_engine.sync_engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log slow queries in debug mode"""
    if settings.DEBUG:
        import time
        context._query_start_time = time.time()


@event.listens_for(async_engine.sync_engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log query execution time in debug mode"""
    if settings.DEBUG and hasattr(context, '_query_start_time'):
        import time
        total = time.time() - context._query_start_time
        if total > 0.1:  # Log queries taking more than 100ms
            logger.warning(f"Slow query ({total:.3f}s): {statement[:100]}...")


# Connection pool monitoring
class ConnectionPoolMonitor:
    """Monitor database connection pool"""
    
    @staticmethod
    def get_pool_status():
        """Get connection pool status"""
        pool = async_engine.pool
        return {
            "size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    
    @staticmethod
    async def log_pool_status():
        """Log connection pool status"""
        status = ConnectionPoolMonitor.get_pool_status()
        logger.info(f"Connection pool status: {status}")


# Export commonly used items
__all__ = [
    "Base",
    "async_engine",
    "sync_engine",
    "AsyncSessionLocal",
    "SessionLocal",
    "get_db",
    "get_db_session",
    "init_db",
    "check_db_health",
    "db_manager",
    "ConnectionPoolMonitor"
]
