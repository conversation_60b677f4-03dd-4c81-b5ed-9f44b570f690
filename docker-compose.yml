# EliteForge AI - Development Docker Compose
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: eliteforge-postgres
    environment:
      POSTGRES_DB: eliteforge
      POSTGRES_USER: eliteforge_user
      POSTGRES_PASSWORD: eliteforge_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/db/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U eliteforge_user -d eliteforge"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - eliteforge-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: eliteforge-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass eliteforge_redis_password
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - eliteforge-network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: eliteforge-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - eliteforge-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: eliteforge-backend
    environment:
      - DATABASE_URL=postgresql+asyncpg://eliteforge_user:eliteforge_password@postgres:5432/eliteforge
      - REDIS_URL=redis://:eliteforge_redis_password@redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
      - DEBUG=true
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
      - STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
      - STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/models:/app/models
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - eliteforge-network

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: eliteforge-frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
      - VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
      - VITE_APP_NAME=EliteForge AI
      - VITE_APP_VERSION=1.0.0
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    command: npm run dev -- --host 0.0.0.0 --port 3000
    networks:
      - eliteforge-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: eliteforge-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./infrastructure/nginx/conf.d:/etc/nginx/conf.d
      - ./infrastructure/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - eliteforge-network

  # AI Services (Optional - for local model serving)
  ai-service:
    build:
      context: ./ai-services
      dockerfile: Dockerfile
    container_name: eliteforge-ai-service
    environment:
      - MODEL_CACHE_DIR=/models
      - HUGGINGFACE_CACHE_DIR=/cache
      - TORCH_HOME=/torch
    ports:
      - "8001:8001"
    volumes:
      - ./ai-services:/app
      - ai_models:/models
      - ai_cache:/cache
      - ai_torch:/torch
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    command: python -m uvicorn main:app --host 0.0.0.0 --port 8001 --reload
    networks:
      - eliteforge-network

  # Celery Worker (Background Tasks)
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: eliteforge-celery-worker
    environment:
      - DATABASE_URL=postgresql+asyncpg://eliteforge_user:eliteforge_password@postgres:5432/eliteforge
      - REDIS_URL=redis://:eliteforge_redis_password@redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
    command: celery -A app.core.celery worker --loglevel=info
    networks:
      - eliteforge-network

  # Celery Beat (Scheduled Tasks)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: eliteforge-celery-beat
    environment:
      - DATABASE_URL=postgresql+asyncpg://eliteforge_user:eliteforge_password@postgres:5432/eliteforge
      - REDIS_URL=redis://:eliteforge_redis_password@redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
    depends_on:
      - postgres
      - redis
    command: celery -A app.core.celery beat --loglevel=info
    networks:
      - eliteforge-network

  # Flower (Celery Monitoring)
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: eliteforge-flower
    environment:
      - REDIS_URL=redis://:eliteforge_redis_password@redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      - redis
    command: celery -A app.core.celery flower --port=5555
    networks:
      - eliteforge-network

  # Prometheus (Metrics)
  prometheus:
    image: prom/prometheus:latest
    container_name: eliteforge-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - eliteforge-network

  # Grafana (Monitoring Dashboard)
  grafana:
    image: grafana/grafana:latest
    container_name: eliteforge-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./infrastructure/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - eliteforge-network

volumes:
  postgres_data:
  redis_data:
  minio_data:
  ai_models:
  ai_cache:
  ai_torch:
  prometheus_data:
  grafana_data:

networks:
  eliteforge-network:
    driver: bridge
