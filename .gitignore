# EliteForge AI - Git Ignore File

# =============================================================================
# Environment and Configuration
# =============================================================================
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
*.env

# =============================================================================
# Python
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# =============================================================================
# Node.js / Frontend
# =============================================================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# =============================================================================
# AI/ML Models and Data
# =============================================================================
# Model files
models/
*.pkl
*.joblib
*.h5
*.hdf5
*.pb
*.onnx
*.tflite
*.pt
*.pth
*.bin
*.safetensors

# Datasets
data/
datasets/
*.csv
*.json
*.parquet
*.feather

# Jupyter notebooks checkpoints
.ipynb_checkpoints/

# MLflow
mlruns/
mlartifacts/

# Weights & Biases
wandb/

# TensorBoard logs
logs/
tensorboard/

# Hugging Face cache
.cache/
transformers_cache/
diffusers_cache/

# =============================================================================
# Docker
# =============================================================================
# Docker files
Dockerfile.prod
docker-compose.override.yml
docker-compose.prod.yml

# =============================================================================
# Database
# =============================================================================
# SQLite
*.db
*.sqlite
*.sqlite3

# PostgreSQL
*.dump
*.sql

# Database backups
backups/
*.backup

# =============================================================================
# IDE and Editor Files
# =============================================================================
# VSCode
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# Operating System Files
# =============================================================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Logs and Temporary Files
# =============================================================================
# Application logs
logs/
*.log
*.log.*

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Backup files
*.bak
*.backup
*.old

# =============================================================================
# Security and Secrets
# =============================================================================
# API keys and secrets
secrets/
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der

# SSL certificates
ssl/
certs/

# =============================================================================
# Build and Distribution
# =============================================================================
# Build directories
build/
dist/
out/

# Generated files
generated/
auto-generated/

# =============================================================================
# Testing
# =============================================================================
# Test results
test-results/
test-reports/
.coverage
coverage.xml
*.cover

# =============================================================================
# Monitoring and Analytics
# =============================================================================
# Prometheus data
prometheus_data/

# Grafana data
grafana_data/

# =============================================================================
# File Uploads and User Content
# =============================================================================
# User uploads
uploads/
media/
static/media/

# Generated images
generated_images/
output/

# =============================================================================
# Cache Directories
# =============================================================================
# General cache
.cache/
cache/

# Redis dumps
dump.rdb

# =============================================================================
# Documentation
# =============================================================================
# Generated documentation
docs/_build/
docs/build/
site/

# =============================================================================
# Deployment
# =============================================================================
# Deployment scripts (if they contain secrets)
deploy.sh
deployment/

# Kubernetes secrets
k8s/secrets/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# =============================================================================
# Miscellaneous
# =============================================================================
# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
