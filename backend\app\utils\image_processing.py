"""
EliteForge AI - Image Processing Utilities
Custom algorithms for image enhancement, watermarking, and optimization
"""

import io
import logging
from typing import Any, Dict, Optional, Tuple
import base64

import numpy as np
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
import cv2

from app.core.config import settings

logger = logging.getLogger(__name__)


class ImageProcessor:
    """Advanced image processing and enhancement"""
    
    def __init__(self):
        self.watermark_font_size = 24
        self.watermark_opacity = 128
        self.enhancement_presets = {
            "subtle": {"contrast": 1.1, "brightness": 1.05, "saturation": 1.1, "sharpness": 1.1},
            "moderate": {"contrast": 1.2, "brightness": 1.1, "saturation": 1.2, "sharpness": 1.2},
            "strong": {"contrast": 1.3, "brightness": 1.15, "saturation": 1.3, "sharpness": 1.3},
        }
    
    async def preprocess_image(
        self, 
        image: Image.Image, 
        target_width: int, 
        target_height: int,
        maintain_aspect: bool = True
    ) -> Image.Image:
        """Preprocess image for AI model input"""
        try:
            # Convert to RGB if needed
            if image.mode != "RGB":
                image = image.convert("RGB")
            
            # Resize image
            if maintain_aspect:
                image = self._resize_with_aspect_ratio(image, target_width, target_height)
            else:
                image = image.resize((target_width, target_height), Image.Resampling.LANCZOS)
            
            # Apply preprocessing enhancements
            image = await self._apply_preprocessing_enhancements(image)
            
            return image
            
        except Exception as e:
            logger.error(f"Image preprocessing failed: {str(e)}")
            raise
    
    async def postprocess_image(
        self, 
        image: Image.Image, 
        parameters: Dict[str, Any] = None
    ) -> Image.Image:
        """Post-process generated image with enhancements"""
        try:
            params = parameters or {}
            
            # Apply enhancement preset if specified
            enhancement_level = params.get("enhancement_level", "subtle")
            if enhancement_level in self.enhancement_presets:
                image = await self._apply_enhancement_preset(image, enhancement_level)
            
            # Apply custom enhancements
            if params.get("custom_enhancements"):
                image = await self._apply_custom_enhancements(image, params["custom_enhancements"])
            
            # Apply noise reduction if requested
            if params.get("noise_reduction", False):
                image = await self._reduce_noise(image)
            
            # Apply upscaling if requested
            upscale_factor = params.get("upscale_factor", 1.0)
            if upscale_factor > 1.0:
                image = await self._upscale_image(image, upscale_factor)
            
            return image
            
        except Exception as e:
            logger.error(f"Image post-processing failed: {str(e)}")
            return image  # Return original if post-processing fails
    
    async def add_watermark(
        self, 
        image: Image.Image, 
        text: str = "EliteForge AI - Free Tier",
        position: str = "bottom_right",
        opacity: int = None
    ) -> Image.Image:
        """Add watermark to image"""
        try:
            # Create a copy to avoid modifying original
            watermarked = image.copy()
            
            # Create watermark overlay
            overlay = Image.new("RGBA", watermarked.size, (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            # Try to load a font, fallback to default
            try:
                font = ImageFont.truetype("arial.ttf", self.watermark_font_size)
            except (OSError, IOError):
                font = ImageFont.load_default()
            
            # Calculate text size and position
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # Determine position
            x, y = self._calculate_watermark_position(
                watermarked.size, text_width, text_height, position
            )
            
            # Draw watermark with semi-transparent background
            padding = 10
            bg_opacity = opacity or self.watermark_opacity
            
            # Background rectangle
            draw.rectangle(
                [x - padding, y - padding, x + text_width + padding, y + text_height + padding],
                fill=(0, 0, 0, bg_opacity // 2)
            )
            
            # Text
            draw.text((x, y), text, font=font, fill=(255, 255, 255, bg_opacity))
            
            # Composite watermark onto image
            if watermarked.mode != "RGBA":
                watermarked = watermarked.convert("RGBA")
            
            watermarked = Image.alpha_composite(watermarked, overlay)
            
            # Convert back to RGB if needed
            if watermarked.mode == "RGBA":
                background = Image.new("RGB", watermarked.size, (255, 255, 255))
                background.paste(watermarked, mask=watermarked.split()[-1])
                watermarked = background
            
            return watermarked
            
        except Exception as e:
            logger.error(f"Watermarking failed: {str(e)}")
            return image  # Return original if watermarking fails
    
    async def enhance_image_quality(
        self, 
        image: Image.Image, 
        enhancement_type: str = "auto"
    ) -> Image.Image:
        """Apply advanced image quality enhancements"""
        try:
            if enhancement_type == "auto":
                # Analyze image and determine best enhancement
                enhancement_type = await self._analyze_image_for_enhancement(image)
            
            if enhancement_type == "portrait":
                return await self._enhance_portrait(image)
            elif enhancement_type == "landscape":
                return await self._enhance_landscape(image)
            elif enhancement_type == "artistic":
                return await self._enhance_artistic(image)
            else:
                return await self._enhance_general(image)
                
        except Exception as e:
            logger.error(f"Image enhancement failed: {str(e)}")
            return image
    
    def _resize_with_aspect_ratio(
        self, 
        image: Image.Image, 
        target_width: int, 
        target_height: int
    ) -> Image.Image:
        """Resize image while maintaining aspect ratio"""
        original_width, original_height = image.size
        aspect_ratio = original_width / original_height
        target_aspect_ratio = target_width / target_height
        
        if aspect_ratio > target_aspect_ratio:
            # Image is wider than target
            new_width = target_width
            new_height = int(target_width / aspect_ratio)
        else:
            # Image is taller than target
            new_height = target_height
            new_width = int(target_height * aspect_ratio)
        
        # Resize and center on target canvas
        resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # Create target canvas and paste resized image
        canvas = Image.new("RGB", (target_width, target_height), (0, 0, 0))
        x_offset = (target_width - new_width) // 2
        y_offset = (target_height - new_height) // 2
        canvas.paste(resized, (x_offset, y_offset))
        
        return canvas
    
    async def _apply_preprocessing_enhancements(self, image: Image.Image) -> Image.Image:
        """Apply subtle enhancements for better AI processing"""
        # Slight contrast and sharpness boost for better AI input
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.05)
        
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.05)
        
        return image
    
    async def _apply_enhancement_preset(self, image: Image.Image, preset: str) -> Image.Image:
        """Apply predefined enhancement preset"""
        settings = self.enhancement_presets[preset]
        
        # Apply contrast
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(settings["contrast"])
        
        # Apply brightness
        enhancer = ImageEnhance.Brightness(image)
        image = enhancer.enhance(settings["brightness"])
        
        # Apply color saturation
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(settings["saturation"])
        
        # Apply sharpness
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(settings["sharpness"])
        
        return image
    
    async def _apply_custom_enhancements(
        self, 
        image: Image.Image, 
        enhancements: Dict[str, float]
    ) -> Image.Image:
        """Apply custom enhancement values"""
        if "contrast" in enhancements:
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(enhancements["contrast"])
        
        if "brightness" in enhancements:
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(enhancements["brightness"])
        
        if "saturation" in enhancements:
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(enhancements["saturation"])
        
        if "sharpness" in enhancements:
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(enhancements["sharpness"])
        
        return image
    
    async def _reduce_noise(self, image: Image.Image) -> Image.Image:
        """Apply noise reduction using OpenCV"""
        try:
            # Convert PIL to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Apply Non-local Means Denoising
            denoised = cv2.fastNlMeansDenoisingColored(cv_image, None, 10, 10, 7, 21)
            
            # Convert back to PIL
            denoised_pil = Image.fromarray(cv2.cvtColor(denoised, cv2.COLOR_BGR2RGB))
            
            return denoised_pil
            
        except Exception as e:
            logger.warning(f"Noise reduction failed: {str(e)}")
            return image
    
    async def _upscale_image(self, image: Image.Image, factor: float) -> Image.Image:
        """Upscale image using advanced resampling"""
        try:
            new_width = int(image.width * factor)
            new_height = int(image.height * factor)
            
            # Use LANCZOS for high-quality upscaling
            upscaled = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Apply slight sharpening after upscaling
            enhancer = ImageEnhance.Sharpness(upscaled)
            upscaled = enhancer.enhance(1.1)
            
            return upscaled
            
        except Exception as e:
            logger.warning(f"Image upscaling failed: {str(e)}")
            return image
    
    def _calculate_watermark_position(
        self, 
        image_size: Tuple[int, int], 
        text_width: int, 
        text_height: int, 
        position: str
    ) -> Tuple[int, int]:
        """Calculate watermark position based on image size and position preference"""
        img_width, img_height = image_size
        margin = 20
        
        positions = {
            "top_left": (margin, margin),
            "top_right": (img_width - text_width - margin, margin),
            "bottom_left": (margin, img_height - text_height - margin),
            "bottom_right": (img_width - text_width - margin, img_height - text_height - margin),
            "center": ((img_width - text_width) // 2, (img_height - text_height) // 2),
        }
        
        return positions.get(position, positions["bottom_right"])
    
    async def _analyze_image_for_enhancement(self, image: Image.Image) -> str:
        """Analyze image to determine optimal enhancement type"""
        try:
            # Convert to numpy array for analysis
            img_array = np.array(image)
            
            # Calculate basic statistics
            brightness = np.mean(img_array)
            contrast = np.std(img_array)
            
            # Simple heuristics for enhancement type
            if brightness < 100:
                return "portrait"  # Darker images often benefit from portrait enhancement
            elif contrast > 50:
                return "artistic"  # High contrast suggests artistic content
            else:
                return "landscape"  # Default to landscape enhancement
                
        except Exception:
            return "general"
    
    async def _enhance_portrait(self, image: Image.Image) -> Image.Image:
        """Portrait-specific enhancements"""
        # Boost brightness and reduce contrast slightly
        enhancer = ImageEnhance.Brightness(image)
        image = enhancer.enhance(1.15)
        
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.05)
        
        # Slight saturation boost
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.1)
        
        return image
    
    async def _enhance_landscape(self, image: Image.Image) -> Image.Image:
        """Landscape-specific enhancements"""
        # Boost contrast and saturation
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.2)
        
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.25)
        
        # Slight sharpness boost
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.15)
        
        return image
    
    async def _enhance_artistic(self, image: Image.Image) -> Image.Image:
        """Artistic content enhancements"""
        # Strong saturation and contrast
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.3)
        
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.25)
        
        return image
    
    async def _enhance_general(self, image: Image.Image) -> Image.Image:
        """General purpose enhancements"""
        # Balanced enhancement
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.1)
        
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.1)
        
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.1)
        
        return image


# Global processor instance
image_processor = ImageProcessor()

# Convenience functions
async def preprocess_image(image: Image.Image, width: int, height: int) -> Image.Image:
    """Preprocess image for AI model input"""
    return await image_processor.preprocess_image(image, width, height)

async def postprocess_image(image: Image.Image, parameters: Dict[str, Any] = None) -> Image.Image:
    """Post-process generated image"""
    return await image_processor.postprocess_image(image, parameters)

async def add_watermark(image: Image.Image, text: str = "EliteForge AI - Free Tier") -> Image.Image:
    """Add watermark to image"""
    return await image_processor.add_watermark(image, text)

async def optimize_image(image: Image.Image, quality: int = 85) -> bytes:
    """Optimize image for storage/transmission"""
    try:
        output = io.BytesIO()
        
        # Convert to RGB if needed
        if image.mode in ("RGBA", "P"):
            background = Image.new("RGB", image.size, (255, 255, 255))
            if image.mode == "P":
                image = image.convert("RGBA")
            background.paste(image, mask=image.split()[-1] if image.mode == "RGBA" else None)
            image = background
        
        # Save with optimization
        image.save(output, format="JPEG", quality=quality, optimize=True)
        return output.getvalue()
        
    except Exception as e:
        logger.error(f"Image optimization failed: {str(e)}")
        # Fallback to PNG
        output = io.BytesIO()
        image.save(output, format="PNG", optimize=True)
        return output.getvalue()

__all__ = [
    "ImageProcessor", 
    "image_processor", 
    "preprocess_image", 
    "postprocess_image", 
    "add_watermark", 
    "optimize_image"
]
