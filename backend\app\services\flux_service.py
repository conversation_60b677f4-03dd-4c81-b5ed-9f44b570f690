"""
EliteForge AI - FLUX.1 Service
Advanced image generation service using FLUX.1 models with tier-based access
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional
from uuid import UUID

import torch
import numpy as np
from PIL import Image
from diffusers import FluxPipeline, FluxImg2ImgPipeline, FluxInpaintPipeline
from transformers import CLIPTextModel, CLIPTokenizer, T5EncoderModel, T5TokenizerFast

from app.core.config import settings
from app.core.exceptions import CustomException
from app.utils.file_storage import FileStorageService
from app.utils.image_processing import preprocess_image, postprocess_image

logger = logging.getLogger(__name__)


class FluxConfig:
    """Configuration for FLUX.1 models"""
    
    MODELS = {
        "flux-dev": {
            "model_id": "black-forest-labs/FLUX.1-dev",
            "description": "FLUX.1 Dev - High-quality image generation",
            "tier_required": "advanced",
            "memory_usage": 12288,  # 12GB
            "max_resolution": 2048,
            "guidance_scale_range": (0.0, 30.0),
            "steps_range": (1, 50),
        },
        "flux-schnell": {
            "model_id": "black-forest-labs/FLUX.1-schnell",
            "description": "FLUX.1 Schnell - Fast generation model",
            "tier_required": "pro",
            "memory_usage": 8192,  # 8GB
            "max_resolution": 1024,
            "guidance_scale_range": (0.0, 10.0),
            "steps_range": (1, 8),
        },
    }
    
    DEFAULT_PARAMS = {
        "width": 1024,
        "height": 1024,
        "num_inference_steps": 28,
        "guidance_scale": 3.5,
        "num_images_per_prompt": 1,
        "max_sequence_length": 512,
        "generator": None,
    }
    
    # FLUX.1 specific optimizations
    OPTIMIZATION_SETTINGS = {
        "enable_model_cpu_offload": True,
        "enable_sequential_cpu_offload": False,
        "enable_attention_slicing": True,
        "enable_flash_attention": True,
        "use_memory_efficient_attention": True,
    }


class FluxService:
    """FLUX.1 advanced image generation service"""
    
    def __init__(self):
        self.device = self._get_optimal_device()
        self.loaded_pipelines = {}
        self.file_storage = FileStorageService()
        self.config = FluxConfig()
        
        # Memory management
        self.max_memory_usage = settings.MAX_MODEL_MEMORY
        self.current_memory_usage = 0
        
        logger.info(f"FluxService initialized on device: {self.device}")
    
    def _get_optimal_device(self) -> str:
        """Determine optimal device for FLUX.1 inference"""
        if torch.cuda.is_available():
            # Check VRAM availability
            total_memory = torch.cuda.get_device_properties(0).total_memory
            if total_memory >= 12 * 1024**3:  # 12GB minimum for FLUX.1
                return f"cuda:{torch.cuda.current_device()}"
            else:
                logger.warning(f"GPU has only {total_memory / 1024**3:.1f}GB VRAM, may not be sufficient for FLUX.1")
                return f"cuda:{torch.cuda.current_device()}"
        elif torch.backends.mps.is_available():
            return "mps"
        else:
            logger.warning("FLUX.1 requires significant memory, CPU inference will be very slow")
            return "cpu"
    
    async def load_pipeline(
        self, 
        model_name: str = "flux-schnell", 
        pipeline_type: str = "txt2img"
    ) -> Any:
        """Load FLUX.1 pipeline with advanced optimizations"""
        
        cache_key = f"flux_{model_name}_{pipeline_type}"
        
        if cache_key in self.loaded_pipelines:
            logger.info(f"Using cached FLUX pipeline: {cache_key}")
            return self.loaded_pipelines[cache_key]
        
        model_config = self.config.MODELS.get(model_name)
        if not model_config:
            raise CustomException(
                status_code=400,
                error_code="FLUX_MODEL_NOT_FOUND",
                message=f"FLUX model {model_name} not found"
            )
        
        # Check memory requirements
        if not await self._check_memory_requirements(model_config["memory_usage"]):
            await self._free_memory()
        
        try:
            logger.info(f"Loading FLUX pipeline: {cache_key}")
            start_time = time.time()
            
            model_id = model_config["model_id"]
            
            # Select pipeline class
            if pipeline_type == "txt2img":
                pipeline_class = FluxPipeline
            elif pipeline_type == "img2img":
                pipeline_class = FluxImg2ImgPipeline
            elif pipeline_type == "inpaint":
                pipeline_class = FluxInpaintPipeline
            else:
                raise ValueError(f"Unknown FLUX pipeline type: {pipeline_type}")
            
            # Load with optimizations
            pipeline = pipeline_class.from_pretrained(
                model_id,
                torch_dtype=torch.bfloat16 if self.device.startswith("cuda") else torch.float32,
                cache_dir=settings.MODEL_CACHE_DIR,
                use_safetensors=True,
                variant="fp16" if self.device.startswith("cuda") else None,
            )
            
            # Apply FLUX-specific optimizations
            await self._optimize_flux_pipeline(pipeline, model_config)
            
            # Move to device
            pipeline = pipeline.to(self.device)
            
            load_time = time.time() - start_time
            logger.info(f"FLUX pipeline {cache_key} loaded in {load_time:.2f}s")
            
            self.loaded_pipelines[cache_key] = pipeline
            self.current_memory_usage += model_config["memory_usage"]
            
            return pipeline
            
        except Exception as e:
            logger.error(f"Failed to load FLUX pipeline {cache_key}: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="FLUX_PIPELINE_LOAD_FAILED",
                message=f"Failed to load FLUX pipeline: {str(e)}"
            )
    
    async def _optimize_flux_pipeline(self, pipeline: Any, model_config: Dict):
        """Apply FLUX.1 specific optimizations"""
        try:
            opt_settings = self.config.OPTIMIZATION_SETTINGS
            
            # Enable model CPU offload for memory efficiency
            if opt_settings["enable_model_cpu_offload"] and hasattr(pipeline, "enable_model_cpu_offload"):
                pipeline.enable_model_cpu_offload()
                logger.debug("Enabled FLUX model CPU offload")
            
            # Enable sequential CPU offload for extreme memory savings
            if opt_settings["enable_sequential_cpu_offload"] and hasattr(pipeline, "enable_sequential_cpu_offload"):
                pipeline.enable_sequential_cpu_offload()
                logger.debug("Enabled FLUX sequential CPU offload")
            
            # Enable attention optimizations
            if opt_settings["enable_attention_slicing"] and hasattr(pipeline, "enable_attention_slicing"):
                pipeline.enable_attention_slicing()
                logger.debug("Enabled FLUX attention slicing")
            
            # Enable Flash Attention if available
            if opt_settings["enable_flash_attention"]:
                try:
                    # This would require flash-attn package
                    if hasattr(pipeline, "enable_flash_attention"):
                        pipeline.enable_flash_attention()
                        logger.debug("Enabled FLUX Flash Attention")
                except Exception as e:
                    logger.warning(f"Could not enable Flash Attention: {str(e)}")
            
            # Compile transformer for faster inference (PyTorch 2.0+)
            if hasattr(torch, "compile") and self.device.startswith("cuda"):
                try:
                    if hasattr(pipeline, "transformer"):
                        pipeline.transformer = torch.compile(
                            pipeline.transformer, 
                            mode="max-autotune",
                            fullgraph=True
                        )
                        logger.debug("Compiled FLUX transformer")
                except Exception as e:
                    logger.warning(f"Could not compile FLUX transformer: {str(e)}")
                    
        except Exception as e:
            logger.warning(f"FLUX pipeline optimization failed: {str(e)}")
    
    async def generate_image(
        self,
        prompt: str,
        model_name: str = "flux-schnell",
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate high-quality image using FLUX.1"""
        
        # Validate model access and parameters
        model_config = self.config.MODELS.get(model_name)
        if not model_config:
            raise CustomException(
                status_code=400,
                error_code="FLUX_MODEL_NOT_FOUND",
                message=f"FLUX model {model_name} not available"
            )
        
        # Merge and validate parameters
        params = {**self.config.DEFAULT_PARAMS, **(parameters or {})}
        params = self._validate_flux_parameters(params, model_config)
        
        # Load pipeline
        pipeline = await self.load_pipeline(model_name, "txt2img")
        
        try:
            logger.info(f"Generating FLUX image with prompt: {prompt[:100]}...")
            start_time = time.time()
            
            # Prepare generation parameters
            generation_params = {
                "prompt": prompt,
                "width": params["width"],
                "height": params["height"],
                "num_inference_steps": params["num_inference_steps"],
                "guidance_scale": params["guidance_scale"],
                "num_images_per_prompt": params["num_images_per_prompt"],
                "max_sequence_length": params["max_sequence_length"],
            }
            
            # Add generator if seed is specified
            if params.get("seed") is not None and params["seed"] >= 0:
                generation_params["generator"] = torch.Generator(device=self.device).manual_seed(params["seed"])
            
            # Generate with FLUX.1
            with torch.inference_mode():
                result = pipeline(**generation_params)
            
            generation_time = time.time() - start_time
            
            # Process and save images
            image_urls = []
            for i, image in enumerate(result.images):
                # FLUX.1 generates high-quality images, minimal post-processing needed
                processed_image = await self._postprocess_flux_image(image, params)
                
                # Save image with high quality
                image_url = await self.file_storage.save_generated_image(
                    processed_image,
                    f"flux_{model_name}_{int(time.time())}_{i}.png",
                    quality=95
                )
                image_urls.append(image_url)
            
            logger.info(f"Generated {len(image_urls)} FLUX images in {generation_time:.2f}s")
            
            return {
                "images": image_urls,
                "parameters": params,
                "model_name": model_name,
                "model_type": "flux",
                "generation_time": generation_time,
                "prompt": prompt,
                "resolution": f"{params['width']}x{params['height']}",
                "quality_score": self._calculate_quality_score(result.images[0]) if result.images else 0
            }
            
        except Exception as e:
            logger.error(f"FLUX image generation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="FLUX_GENERATION_FAILED",
                message=f"FLUX image generation failed: {str(e)}"
            )
    
    async def image_to_image(
        self,
        prompt: str,
        init_image: Image.Image,
        strength: float = 0.8,
        model_name: str = "flux-schnell",
        parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """FLUX.1 image-to-image generation"""
        
        model_config = self.config.MODELS.get(model_name)
        if not model_config:
            raise CustomException(
                status_code=400,
                error_code="FLUX_MODEL_NOT_FOUND",
                message=f"FLUX model {model_name} not available"
            )
        
        params = {**self.config.DEFAULT_PARAMS, **(parameters or {})}
        params = self._validate_flux_parameters(params, model_config)
        
        # Load img2img pipeline
        pipeline = await self.load_pipeline(model_name, "img2img")
        
        try:
            logger.info(f"Generating FLUX img2img with prompt: {prompt[:100]}...")
            start_time = time.time()
            
            # Preprocess input image for FLUX
            init_image = await self._preprocess_flux_image(init_image, params["width"], params["height"])
            
            generation_params = {
                "prompt": prompt,
                "image": init_image,
                "strength": strength,
                "num_inference_steps": params["num_inference_steps"],
                "guidance_scale": params["guidance_scale"],
                "num_images_per_prompt": params["num_images_per_prompt"],
            }
            
            with torch.inference_mode():
                result = pipeline(**generation_params)
            
            generation_time = time.time() - start_time
            
            # Save generated images
            image_urls = []
            for i, image in enumerate(result.images):
                processed_image = await self._postprocess_flux_image(image, params)
                image_url = await self.file_storage.save_generated_image(
                    processed_image,
                    f"flux_img2img_{model_name}_{int(time.time())}_{i}.png",
                    quality=95
                )
                image_urls.append(image_url)
            
            return {
                "images": image_urls,
                "parameters": params,
                "model_name": model_name,
                "model_type": "flux",
                "generation_time": generation_time,
                "strength": strength,
                "quality_score": self._calculate_quality_score(result.images[0]) if result.images else 0
            }
            
        except Exception as e:
            logger.error(f"FLUX img2img generation failed: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="FLUX_IMG2IMG_FAILED",
                message=f"FLUX img2img generation failed: {str(e)}"
            )
    
    def _validate_flux_parameters(self, params: Dict[str, Any], model_config: Dict) -> Dict[str, Any]:
        """Validate FLUX.1 specific parameters"""
        validated = params.copy()
        
        max_res = model_config["max_resolution"]
        
        # Validate dimensions (FLUX.1 supports higher resolutions)
        validated["width"] = max(256, min(max_res, validated.get("width", 1024)))
        validated["height"] = max(256, min(max_res, validated.get("height", 1024)))
        
        # FLUX.1 works best with multiples of 16
        validated["width"] = (validated["width"] // 16) * 16
        validated["height"] = (validated["height"] // 16) * 16
        
        # Validate inference steps based on model
        steps_range = model_config["steps_range"]
        validated["num_inference_steps"] = max(steps_range[0], min(steps_range[1], validated.get("num_inference_steps", 28)))
        
        # Validate guidance scale
        guidance_range = model_config["guidance_scale_range"]
        validated["guidance_scale"] = max(guidance_range[0], min(guidance_range[1], validated.get("guidance_scale", 3.5)))
        
        # Validate number of images (FLUX.1 is resource intensive)
        validated["num_images_per_prompt"] = max(1, min(2, validated.get("num_images_per_prompt", 1)))
        
        # Validate sequence length
        validated["max_sequence_length"] = max(77, min(512, validated.get("max_sequence_length", 512)))
        
        return validated
    
    async def _preprocess_flux_image(self, image: Image.Image, width: int, height: int) -> Image.Image:
        """Preprocess image for FLUX.1 input"""
        # FLUX.1 specific preprocessing
        image = image.convert("RGB")
        image = image.resize((width, height), Image.Resampling.LANCZOS)
        return image
    
    async def _postprocess_flux_image(self, image: Image.Image, params: Dict) -> Image.Image:
        """Post-process FLUX.1 generated image"""
        # FLUX.1 typically generates high-quality images with minimal post-processing needed
        # Apply any custom enhancements here
        return image
    
    def _calculate_quality_score(self, image: Image.Image) -> float:
        """Calculate a quality score for the generated image"""
        try:
            # Simple quality metrics based on image properties
            img_array = np.array(image)
            
            # Calculate variance (higher variance often indicates more detail)
            variance = np.var(img_array)
            
            # Calculate edge density using simple gradient
            gray = np.mean(img_array, axis=2)
            edges = np.abs(np.gradient(gray)).sum()
            
            # Normalize to 0-100 scale
            quality_score = min(100, (variance / 1000 + edges / 100000) * 10)
            
            return round(quality_score, 2)
        except Exception:
            return 0.0
    
    async def _check_memory_requirements(self, required_mb: int) -> bool:
        """Check if enough memory is available for model loading"""
        if self.current_memory_usage + required_mb > self.max_memory_usage:
            return False
        
        if torch.cuda.is_available():
            available_memory = torch.cuda.get_device_properties(0).total_memory / (1024 * 1024)
            current_memory = torch.cuda.memory_allocated() / (1024 * 1024)
            return (available_memory - current_memory) >= required_mb
        
        return True
    
    async def _free_memory(self):
        """Free memory by unloading models"""
        if self.loaded_pipelines:
            # Remove least recently used pipeline
            oldest_key = next(iter(self.loaded_pipelines))
            del self.loaded_pipelines[oldest_key]
            
            # Update memory usage
            self.current_memory_usage = max(0, self.current_memory_usage - 8192)  # Estimate
            
            # Force cleanup
            import gc
            gc.collect()
            
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            logger.info(f"Freed FLUX memory by unloading: {oldest_key}")
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get list of available FLUX.1 models"""
        models = []
        for name, config in self.config.MODELS.items():
            models.append({
                "name": name,
                "model_id": config["model_id"],
                "description": config["description"],
                "tier_required": config["tier_required"],
                "memory_usage_mb": config["memory_usage"],
                "max_resolution": config["max_resolution"],
                "steps_range": config["steps_range"],
                "guidance_range": config["guidance_scale_range"]
            })
        return models
    
    async def clear_cache(self):
        """Clear all loaded FLUX pipelines"""
        self.loaded_pipelines.clear()
        self.current_memory_usage = 0
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        import gc
        gc.collect()
        
        logger.info("Cleared FLUX cache")


# Global service instance
flux_service = FluxService()

__all__ = ["FluxService", "flux_service", "FluxConfig"]
