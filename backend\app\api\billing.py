"""
EliteForge AI - Billing API Endpoints
RESTful API for billing, subscriptions, credits, and invoices
"""

import logging
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.auth import get_current_user
from app.core.database import get_db
from app.core.exceptions import CustomException
from app.db.models import InvoiceStatus, RequestType, SubscriptionTier, User
from app.services.billing_service import BillingService
from app.services.invoice_service import InvoiceService
from app.services.subscription_service import SubscriptionManager

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/billing", tags=["billing"])


# Pydantic models for request/response
class SubscriptionCreateRequest(BaseModel):
    tier: SubscriptionTier
    payment_method_id: Optional[str] = None
    trial_days: int = 7


class SubscriptionUpgradeRequest(BaseModel):
    new_tier: SubscriptionTier


class CreditPurchaseRequest(BaseModel):
    package_name: str
    payment_method_id: Optional[str] = None


class UsageCostRequest(BaseModel):
    request_type: RequestType
    parameters: Optional[Dict[str, Any]] = None
    tokens_used: int = 0
    processing_time: float = 0


# Subscription endpoints
@router.post("/subscriptions")
async def create_subscription(
    request: SubscriptionCreateRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create new subscription"""
    try:
        subscription_manager = SubscriptionManager(db)
        subscription = await subscription_manager.create_subscription(
            user_id=current_user.id,
            tier=request.tier,
            payment_method_id=request.payment_method_id,
            trial_days=request.trial_days
        )
        
        return {
            "id": str(subscription.id),
            "tier": subscription.tier.value,
            "status": subscription.status.value,
            "created_at": subscription.created_at.isoformat(),
            "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None
        }
        
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.error(f"Subscription creation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Subscription creation failed")


@router.get("/subscriptions/current")
async def get_current_subscription(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's current subscription"""
    try:
        subscription_manager = SubscriptionManager(db)
        subscription = await subscription_manager.get_user_subscription(current_user.id)
        
        if not subscription:
            return {"subscription": None, "tier": "free"}
        
        return {
            "subscription": {
                "id": str(subscription.id),
                "tier": subscription.tier.value,
                "status": subscription.status.value,
                "monthly_requests_used": subscription.monthly_requests_used,
                "monthly_request_limit": subscription.monthly_request_limit,
                "current_period_start": subscription.current_period_start.isoformat() if subscription.current_period_start else None,
                "current_period_end": subscription.current_period_end.isoformat() if subscription.current_period_end else None,
                "cancel_at_period_end": subscription.cancel_at_period_end,
                "created_at": subscription.created_at.isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get current subscription: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get subscription")


@router.put("/subscriptions/{subscription_id}/upgrade")
async def upgrade_subscription(
    subscription_id: UUID,
    request: SubscriptionUpgradeRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Upgrade/downgrade subscription"""
    try:
        subscription_manager = SubscriptionManager(db)
        subscription = await subscription_manager.upgrade_subscription(
            subscription_id=subscription_id,
            new_tier=request.new_tier
        )
        
        return {
            "id": str(subscription.id),
            "tier": subscription.tier.value,
            "status": subscription.status.value,
            "updated_at": subscription.updated_at.isoformat()
        }
        
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.error(f"Subscription upgrade failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Subscription upgrade failed")


@router.delete("/subscriptions/{subscription_id}")
async def cancel_subscription(
    subscription_id: UUID,
    cancel_immediately: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Cancel subscription"""
    try:
        subscription_manager = SubscriptionManager(db)
        subscription = await subscription_manager.cancel_subscription(
            subscription_id=subscription_id,
            cancel_immediately=cancel_immediately
        )
        
        return {
            "id": str(subscription.id),
            "status": subscription.status.value,
            "cancelled_at": subscription.cancelled_at.isoformat() if subscription.cancelled_at else None,
            "cancel_at_period_end": subscription.cancel_at_period_end
        }
        
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.error(f"Subscription cancellation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Subscription cancellation failed")


@router.get("/subscriptions/analytics")
async def get_subscription_analytics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get subscription analytics"""
    try:
        subscription_manager = SubscriptionManager(db)
        analytics = await subscription_manager.get_subscription_analytics(current_user.id)
        
        return analytics
        
    except Exception as e:
        logger.error(f"Failed to get subscription analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get analytics")


# Credits endpoints
@router.get("/credits")
async def get_credits(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's credit balance"""
    try:
        billing_service = BillingService(db)
        credits = await billing_service.get_user_credits(current_user.id)
        
        return credits
        
    except Exception as e:
        logger.error(f"Failed to get credits: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get credits")


@router.post("/credits/purchase")
async def purchase_credits(
    request: CreditPurchaseRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Purchase credit package"""
    try:
        billing_service = BillingService(db)
        result = await billing_service.purchase_credits(
            user_id=current_user.id,
            package_name=request.package_name,
            payment_method_id=request.payment_method_id
        )
        
        return result
        
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        logger.error(f"Credit purchase failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Credit purchase failed")


@router.get("/credits/packages")
async def get_credit_packages(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get available credit packages"""
    try:
        billing_service = BillingService(db)
        summary = await billing_service.get_billing_summary(current_user.id)
        
        return {"packages": summary.get("available_credit_packages", [])}
        
    except Exception as e:
        logger.error(f"Failed to get credit packages: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get credit packages")


# Usage and billing endpoints
@router.post("/usage/calculate-cost")
async def calculate_usage_cost(
    request: UsageCostRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Calculate cost for AI request"""
    try:
        billing_service = BillingService(db)
        cost_cents = await billing_service.calculate_request_cost(
            request_type=request.request_type,
            parameters=request.parameters,
            tokens_used=request.tokens_used,
            processing_time=request.processing_time
        )
        
        return {
            "cost_cents": cost_cents,
            "cost_dollars": cost_cents / 100
        }
        
    except Exception as e:
        logger.error(f"Cost calculation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Cost calculation failed")


@router.get("/usage/report")
async def get_usage_report(
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get usage report"""
    try:
        from datetime import datetime, timedelta
        
        billing_service = BillingService(db)
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        report = await billing_service.generate_usage_report(
            user_id=current_user.id,
            start_date=start_date,
            end_date=end_date
        )
        
        return report
        
    except Exception as e:
        logger.error(f"Usage report generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Usage report generation failed")


@router.get("/summary")
async def get_billing_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive billing summary"""
    try:
        billing_service = BillingService(db)
        summary = await billing_service.get_billing_summary(current_user.id)
        
        return summary
        
    except Exception as e:
        logger.error(f"Billing summary generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Billing summary generation failed")


# Invoice endpoints
@router.get("/invoices")
async def get_invoices(
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status: Optional[InvoiceStatus] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's invoices"""
    try:
        invoice_service = InvoiceService(db)
        invoices = await invoice_service.get_user_invoices(
            user_id=current_user.id,
            limit=limit,
            offset=offset,
            status_filter=status
        )
        
        return invoices
        
    except Exception as e:
        logger.error(f"Failed to get invoices: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get invoices")


@router.get("/invoices/{invoice_id}")
async def get_invoice(
    invoice_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get specific invoice"""
    try:
        invoice_service = InvoiceService(db)
        invoices = await invoice_service.get_user_invoices(
            user_id=current_user.id,
            limit=1,
            offset=0
        )
        
        # Find the specific invoice
        invoice = next((inv for inv in invoices["invoices"] if inv["id"] == str(invoice_id)), None)
        
        if not invoice:
            raise HTTPException(status_code=404, detail="Invoice not found")
        
        return invoice
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get invoice: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get invoice")


@router.get("/invoices/summary")
async def get_invoice_summary(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get invoice billing summary"""
    try:
        invoice_service = InvoiceService(db)
        summary = await invoice_service.get_billing_summary(current_user.id)
        
        return summary
        
    except Exception as e:
        logger.error(f"Invoice summary generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Invoice summary generation failed")


# Usage limits endpoint
@router.get("/limits/check")
async def check_usage_limits(
    request_type: RequestType = Query(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Check usage limits for request type"""
    try:
        subscription_manager = SubscriptionManager(db)
        limits = await subscription_manager.check_usage_limits(
            user_id=current_user.id,
            request_type=request_type
        )
        
        return limits
        
    except Exception as e:
        logger.error(f"Usage limit check failed: {str(e)}")
        raise HTTPException(status_code=500, detail="Usage limit check failed")
