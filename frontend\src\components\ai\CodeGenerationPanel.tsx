import React, { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { 
  CodeBracketIcon, 
  ClipboardDocumentIcon, 
  ArrowDownTrayIcon,
  AdjustmentsHorizontalIcon,
  PlayIcon
} from '@heroicons/react/24/outline'
import { aiApi } from '../../lib/api'

interface CodeGenerationPanelProps {
  onGenerationStart: () => void
  onGenerationComplete: () => void
  onGenerationError: () => void
  isGenerating: boolean
  subscription?: any
  models: any[]
}

const codeModels = [
  { id: 'code-llama-7b', name: 'CodeLlama 7B', description: 'General code generation' },
  { id: 'code-llama-13b', name: 'CodeLlama 13B', description: 'Advanced code generation (Advanced+)' },
  { id: 'custom-code', name: 'Custom Code Model', description: 'Specialized coding (Enterprise)' },
]

const languages = [
  { id: 'python', name: 'Python', extension: '.py' },
  { id: 'javascript', name: 'JavaScript', extension: '.js' },
  { id: 'typescript', name: 'TypeScript', extension: '.ts' },
  { id: 'java', name: 'Java', extension: '.java' },
  { id: 'cpp', name: 'C++', extension: '.cpp' },
  { id: 'csharp', name: 'C#', extension: '.cs' },
  { id: 'go', name: 'Go', extension: '.go' },
  { id: 'rust', name: 'Rust', extension: '.rs' },
  { id: 'php', name: 'PHP', extension: '.php' },
  { id: 'ruby', name: 'Ruby', extension: '.rb' },
  { id: 'swift', name: 'Swift', extension: '.swift' },
  { id: 'kotlin', name: 'Kotlin', extension: '.kt' },
]

const codePresets = [
  {
    name: 'Function',
    prompt: 'Write a function that',
    language: 'python',
  },
  {
    name: 'Class',
    prompt: 'Create a class that',
    language: 'python',
  },
  {
    name: 'API Endpoint',
    prompt: 'Create an API endpoint that',
    language: 'javascript',
  },
  {
    name: 'Algorithm',
    prompt: 'Implement an algorithm that',
    language: 'python',
  },
  {
    name: 'Database Query',
    prompt: 'Write a SQL query that',
    language: 'sql',
  },
  {
    name: 'Unit Test',
    prompt: 'Write unit tests for',
    language: 'python',
  },
]

export default function CodeGenerationPanel({
  onGenerationStart,
  onGenerationComplete,
  onGenerationError,
  isGenerating,
  subscription,
  models
}: CodeGenerationPanelProps) {
  const [prompt, setPrompt] = useState('')
  const [selectedModel, setSelectedModel] = useState('code-llama-7b')
  const [selectedLanguage, setSelectedLanguage] = useState('python')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [parameters, setParameters] = useState({
    max_tokens: 1000,
    temperature: 0.2,
  })
  const [result, setResult] = useState<any>(null)

  const generateMutation = useMutation({
    mutationFn: (data: any) => aiApi.generateCode(data),
    onMutate: () => {
      onGenerationStart()
      setResult(null)
    },
    onSuccess: (response) => {
      setResult(response.data)
      onGenerationComplete()
    },
    onError: (error) => {
      console.error('Code generation failed:', error)
      onGenerationError()
    },
  })

  const handleGenerate = () => {
    if (!prompt.trim()) return

    generateMutation.mutate({
      prompt: prompt.trim(),
      language: selectedLanguage,
      model: selectedModel,
      ...parameters,
    })
  }

  const handlePresetSelect = (preset: typeof codePresets[0]) => {
    setPrompt(preset.prompt)
    setSelectedLanguage(preset.language)
  }

  const handleCopyCode = () => {
    if (result?.result) {
      navigator.clipboard.writeText(result.result)
    }
  }

  const handleDownloadCode = () => {
    if (result?.result) {
      const language = languages.find(lang => lang.id === selectedLanguage)
      const extension = language?.extension || '.txt'
      const blob = new Blob([result.result], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `generated-code${extension}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  const canUseModel = (modelId: string) => {
    if (!subscription) return false
    const tier = subscription.tier.toLowerCase()
    
    if (modelId.includes('13b') && !['advanced', 'enterprise'].includes(tier)) return false
    if (modelId.includes('custom') && tier !== 'enterprise') return false
    
    return true
  }

  const getLanguageColor = (lang: string) => {
    const colors: Record<string, string> = {
      python: 'text-blue-600',
      javascript: 'text-yellow-600',
      typescript: 'text-blue-700',
      java: 'text-red-600',
      cpp: 'text-purple-600',
      csharp: 'text-green-600',
      go: 'text-cyan-600',
      rust: 'text-orange-600',
      php: 'text-indigo-600',
      ruby: 'text-red-500',
      swift: 'text-orange-500',
      kotlin: 'text-purple-500',
    }
    return colors[lang] || 'text-gray-600'
  }

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <CodeBracketIcon className="h-6 w-6 text-green-500 mr-3" />
        <h3 className="text-lg font-semibold text-gray-900">Code Generation</h3>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          {/* Code Presets */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Code Templates
            </label>
            <div className="grid grid-cols-2 gap-2">
              {codePresets.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => handlePresetSelect(preset)}
                  disabled={isGenerating}
                  className="p-2 text-left border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="text-sm font-medium text-gray-900">{preset.name}</div>
                  <div className={`text-xs ${getLanguageColor(preset.language)}`}>
                    {preset.language}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              disabled={isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            >
              {codeModels.map((model) => (
                <option 
                  key={model.id} 
                  value={model.id}
                  disabled={!canUseModel(model.id)}
                >
                  {model.name} - {model.description}
                  {!canUseModel(model.id) ? ' (Upgrade Required)' : ''}
                </option>
              ))}
            </select>
          </div>

          {/* Language Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Programming Language
            </label>
            <select
              value={selectedLanguage}
              onChange={(e) => setSelectedLanguage(e.target.value)}
              disabled={isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            >
              {languages.map((language) => (
                <option key={language.id} value={language.id}>
                  {language.name}
                </option>
              ))}
            </select>
          </div>

          {/* Code Prompt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Code Description
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              disabled={isGenerating}
              placeholder="Describe the code you want to generate..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
              rows={4}
            />
          </div>

          {/* Advanced Parameters */}
          <div>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-sm text-primary-600 hover:text-primary-700"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-1" />
              Advanced Parameters
            </button>
            
            {showAdvanced && (
              <div className="mt-3 space-y-3 p-3 bg-gray-50 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Tokens: {parameters.max_tokens}
                  </label>
                  <input
                    type="range"
                    min="100"
                    max="2000"
                    value={parameters.max_tokens}
                    onChange={(e) => setParameters(prev => ({ ...prev, max_tokens: parseInt(e.target.value) }))}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Temperature: {parameters.temperature}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={parameters.temperature}
                    onChange={(e) => setParameters(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                    disabled={isGenerating}
                    className="w-full"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    Lower values = more deterministic, Higher values = more creative
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Generate Button */}
          <button
            onClick={handleGenerate}
            disabled={!prompt.trim() || isGenerating}
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating...
              </>
            ) : (
              <>
                <CodeBracketIcon className="h-4 w-4 mr-2" />
                Generate Code
              </>
            )}
          </button>
        </div>

        {/* Result Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">Generated Code</h4>
            {result && (
              <div className="flex space-x-2">
                <button
                  onClick={handleCopyCode}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Copy code"
                >
                  <ClipboardDocumentIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={handleDownloadCode}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Download as file"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>

          <div className="border border-gray-200 rounded-lg overflow-hidden min-h-[400px] bg-gray-50">
            {isGenerating ? (
              <div className="flex items-center justify-center h-full p-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
                  <p className="text-gray-500">Generating your code...</p>
                </div>
              </div>
            ) : result ? (
              <div className="h-full">
                {/* Code Header */}
                <div className="bg-gray-800 text-white px-4 py-2 flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${getLanguageColor(selectedLanguage).replace('text-', 'bg-')}`}></div>
                    <span className="text-sm font-medium">
                      {languages.find(lang => lang.id === selectedLanguage)?.name}
                    </span>
                  </div>
                  <div className="text-xs text-gray-300">
                    {result.processing_time?.toFixed(2)}s
                  </div>
                </div>
                
                {/* Code Content */}
                <div className="p-4 bg-gray-900 text-gray-100 overflow-x-auto">
                  <pre className="text-sm font-mono whitespace-pre-wrap">
                    <code>{result.result}</code>
                  </pre>
                </div>
                
                {/* Generation Info */}
                <div className="p-3 bg-gray-50 border-t border-gray-200 text-xs text-gray-500 space-y-1">
                  <div>Model: {selectedModel}</div>
                  <div>Language: {languages.find(lang => lang.id === selectedLanguage)?.name}</div>
                  {result.cached && <div>Result was cached</div>}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400 p-8">
                <div className="text-center">
                  <CodeBracketIcon className="h-12 w-12 mx-auto mb-2" />
                  <p>Generated code will appear here</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
