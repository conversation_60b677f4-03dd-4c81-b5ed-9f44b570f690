"""
EliteForge AI - AI Service Layer
Unified AI service for model management, request processing, and tier-based access control
"""

import asyncio
import hashlib
import json
import logging
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union
from uuid import UUID

import torch
from PIL import Image
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.redis_client import cache_manager, redis_manager
from app.db.models import AIRequest, RequestStatus, RequestType, SubscriptionTier, User
from app.services.user_service import UserService
from app.services.subscription_service import SubscriptionService
from app.utils.image_processing import add_watermark, optimize_image
from app.core.exceptions import CustomException

logger = logging.getLogger(__name__)


class ModelType(str, Enum):
    """AI Model types"""
    STABLE_DIFFUSION = "stable_diffusion"
    FLUX_1 = "flux_1"
    LLAMA = "llama"
    CODE_LLAMA = "code_llama"
    CUSTOM_VISION = "custom_vision"
    CUSTOM_NLP = "custom_nlp"


class ModelTier(str, Enum):
    """Model access tiers"""
    BASIC = "basic"
    STANDARD = "standard"
    ADVANCED = "advanced"
    ENTERPRISE = "enterprise"


class AIModelManager:
    """Manages AI model loading, caching, and optimization"""
    
    def __init__(self):
        self.loaded_models = {}
        self.model_configs = {
            ModelType.STABLE_DIFFUSION: {
                "model_id": "runwayml/stable-diffusion-v1-5",
                "tier": ModelTier.BASIC,
                "memory_usage": 4096,  # MB
                "gpu_required": True,
                "cache_ttl": 3600,  # 1 hour
            },
            ModelType.FLUX_1: {
                "model_id": "black-forest-labs/FLUX.1-dev",
                "tier": ModelTier.ADVANCED,
                "memory_usage": 8192,  # MB
                "gpu_required": True,
                "cache_ttl": 7200,  # 2 hours
            },
            ModelType.LLAMA: {
                "model_id": "meta-llama/Llama-2-7b-chat-hf",
                "tier": ModelTier.STANDARD,
                "memory_usage": 6144,  # MB
                "gpu_required": False,
                "cache_ttl": 1800,  # 30 minutes
            },
            ModelType.CODE_LLAMA: {
                "model_id": "codellama/CodeLlama-7b-Python-hf",
                "tier": ModelTier.STANDARD,
                "memory_usage": 6144,  # MB
                "gpu_required": False,
                "cache_ttl": 1800,  # 30 minutes
            },
        }
        self.device = self._get_optimal_device()
        self.memory_monitor = ModelMemoryMonitor()
    
    def _get_optimal_device(self) -> str:
        """Determine optimal device for model execution"""
        if torch.cuda.is_available():
            return f"cuda:{torch.cuda.current_device()}"
        elif torch.backends.mps.is_available():
            return "mps"
        else:
            return "cpu"
    
    async def load_model(self, model_type: ModelType) -> Any:
        """Load AI model with caching and optimization"""
        if model_type in self.loaded_models:
            logger.info(f"Using cached model: {model_type}")
            return self.loaded_models[model_type]
        
        config = self.model_configs.get(model_type)
        if not config:
            raise CustomException(
                status_code=400,
                error_code="MODEL_NOT_SUPPORTED",
                message=f"Model type {model_type} is not supported"
            )
        
        # Check memory availability
        if not await self.memory_monitor.check_memory_availability(config["memory_usage"]):
            await self._free_memory()
        
        try:
            logger.info(f"Loading model: {model_type}")
            start_time = time.time()
            
            if model_type in [ModelType.STABLE_DIFFUSION, ModelType.FLUX_1]:
                model = await self._load_diffusion_model(config)
            elif model_type in [ModelType.LLAMA, ModelType.CODE_LLAMA]:
                model = await self._load_language_model(config)
            else:
                raise CustomException(
                    status_code=400,
                    error_code="MODEL_LOAD_FAILED",
                    message=f"Unknown model type: {model_type}"
                )
            
            load_time = time.time() - start_time
            logger.info(f"Model {model_type} loaded in {load_time:.2f}s")
            
            self.loaded_models[model_type] = model
            return model
            
        except Exception as e:
            logger.error(f"Failed to load model {model_type}: {str(e)}")
            raise CustomException(
                status_code=500,
                error_code="MODEL_LOAD_FAILED",
                message=f"Failed to load model: {str(e)}"
            )
    
    async def _load_diffusion_model(self, config: Dict) -> Any:
        """Load diffusion model (Stable Diffusion or FLUX.1)"""
        from diffusers import StableDiffusionPipeline, DiffusionPipeline
        
        model_id = config["model_id"]
        
        if "FLUX" in model_id:
            # FLUX.1 model loading
            pipeline = DiffusionPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if self.device.startswith("cuda") else torch.float32,
                use_safetensors=True,
                cache_dir=settings.MODEL_CACHE_DIR
            )
        else:
            # Stable Diffusion model loading
            pipeline = StableDiffusionPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if self.device.startswith("cuda") else torch.float32,
                use_safetensors=True,
                cache_dir=settings.MODEL_CACHE_DIR
            )
        
        pipeline = pipeline.to(self.device)
        
        # Enable memory efficient attention if available
        if hasattr(pipeline, "enable_attention_slicing"):
            pipeline.enable_attention_slicing()
        
        if hasattr(pipeline, "enable_xformers_memory_efficient_attention"):
            try:
                pipeline.enable_xformers_memory_efficient_attention()
            except Exception:
                pass  # xformers not available
        
        return pipeline
    
    async def _load_language_model(self, config: Dict) -> Any:
        """Load language model (Llama or CodeLlama)"""
        from transformers import AutoTokenizer, AutoModelForCausalLM
        
        model_id = config["model_id"]
        
        tokenizer = AutoTokenizer.from_pretrained(
            model_id,
            cache_dir=settings.MODEL_CACHE_DIR
        )
        
        model = AutoModelForCausalLM.from_pretrained(
            model_id,
            torch_dtype=torch.float16 if self.device.startswith("cuda") else torch.float32,
            device_map="auto" if self.device.startswith("cuda") else None,
            cache_dir=settings.MODEL_CACHE_DIR
        )
        
        return {"model": model, "tokenizer": tokenizer}
    
    async def _free_memory(self):
        """Free memory by unloading least recently used models"""
        if not self.loaded_models:
            return
        
        # Simple LRU - remove oldest model
        oldest_model = next(iter(self.loaded_models))
        del self.loaded_models[oldest_model]
        
        # Force garbage collection
        import gc
        gc.collect()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info(f"Freed memory by unloading model: {oldest_model}")
    
    def get_model_tier(self, model_type: ModelType) -> ModelTier:
        """Get required tier for model access"""
        config = self.model_configs.get(model_type)
        return config["tier"] if config else ModelTier.ENTERPRISE


class ModelMemoryMonitor:
    """Monitor and manage model memory usage"""
    
    def __init__(self):
        self.max_memory_mb = settings.MAX_MODEL_MEMORY
    
    async def check_memory_availability(self, required_mb: int) -> bool:
        """Check if enough memory is available for model loading"""
        try:
            if torch.cuda.is_available():
                current_memory = torch.cuda.memory_allocated() / (1024 * 1024)  # MB
                available_memory = torch.cuda.get_device_properties(0).total_memory / (1024 * 1024) - current_memory
                return available_memory >= required_mb
            else:
                # For CPU, use a simple heuristic
                import psutil
                available_memory = psutil.virtual_memory().available / (1024 * 1024)  # MB
                return available_memory >= required_mb * 2  # Conservative estimate
        except Exception:
            return True  # Assume available if check fails
    
    async def get_memory_stats(self) -> Dict[str, float]:
        """Get current memory usage statistics"""
        stats = {}
        
        try:
            if torch.cuda.is_available():
                stats["gpu_allocated"] = torch.cuda.memory_allocated() / (1024 * 1024)
                stats["gpu_cached"] = torch.cuda.memory_reserved() / (1024 * 1024)
                stats["gpu_total"] = torch.cuda.get_device_properties(0).total_memory / (1024 * 1024)
            
            import psutil
            memory = psutil.virtual_memory()
            stats["cpu_used"] = (memory.total - memory.available) / (1024 * 1024)
            stats["cpu_total"] = memory.total / (1024 * 1024)
            stats["cpu_percent"] = memory.percent
            
        except Exception as e:
            logger.error(f"Failed to get memory stats: {str(e)}")
        
        return stats


class AIService:
    """Main AI service for processing requests"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.model_manager = AIModelManager()
        self.user_service = UserService(db)
        self.subscription_service = SubscriptionService(db)
        self.request_queue = AIRequestQueue()
    
    async def process_ai_request(
        self,
        user_id: UUID,
        request_type: RequestType,
        prompt: str,
        parameters: Dict[str, Any] = None,
        model_name: str = None
    ) -> Dict[str, Any]:
        """Process AI request with tier-based access control"""
        
        # Get user and subscription
        user = await self.user_service.get_user_by_id(user_id)
        if not user:
            raise CustomException(
                status_code=404,
                error_code="USER_NOT_FOUND",
                message="User not found"
            )
        
        subscription = await self.subscription_service.get_user_subscription(user_id)
        if not subscription:
            raise CustomException(
                status_code=400,
                error_code="NO_SUBSCRIPTION",
                message="User has no active subscription"
            )
        
        # Check usage limits
        if not await self._check_usage_limits(subscription):
            raise CustomException(
                status_code=429,
                error_code="USAGE_LIMIT_EXCEEDED",
                message="Monthly usage limit exceeded"
            )
        
        # Determine model type and validate access
        model_type = self._get_model_type(request_type, model_name)
        if not await self._check_model_access(subscription.tier, model_type):
            raise CustomException(
                status_code=403,
                error_code="MODEL_ACCESS_DENIED",
                message=f"Model {model_type} not available for {subscription.tier} tier"
            )
        
        # Create AI request record
        ai_request = await self._create_ai_request(
            user_id, request_type, model_type.value, prompt, parameters or {}
        )
        
        try:
            # Check cache first
            cache_key = self._generate_cache_key(model_type, prompt, parameters or {})
            cached_result = await cache_manager.get_cached_model_output(model_type.value, cache_key)
            
            if cached_result:
                logger.info(f"Using cached result for request {ai_request.id}")
                result = cached_result
            else:
                # Process request
                if subscription.tier in [SubscriptionTier.FREE, SubscriptionTier.BASIC]:
                    # Add to standard queue
                    result = await self.request_queue.add_to_queue(ai_request, priority=0)
                else:
                    # Priority processing for Pro/Enterprise
                    result = await self.request_queue.add_to_queue(ai_request, priority=1)
                
                # Cache result
                await cache_manager.cache_model_output(
                    model_type.value, cache_key, result, expire_hours=1
                )
            
            # Post-process result
            processed_result = await self._post_process_result(
                result, subscription.tier, request_type
            )
            
            # Update request status
            await self._update_ai_request(ai_request.id, RequestStatus.COMPLETED, processed_result)
            
            # Update usage tracking
            await self._update_usage_tracking(user_id, request_type, model_type)
            
            return {
                "request_id": str(ai_request.id),
                "status": "completed",
                "result": processed_result,
                "processing_time": result.get("processing_time", 0),
                "cached": cached_result is not None
            }
            
        except Exception as e:
            logger.error(f"AI request processing failed: {str(e)}")
            await self._update_ai_request(ai_request.id, RequestStatus.FAILED, {"error": str(e)})
            raise CustomException(
                status_code=500,
                error_code="AI_PROCESSING_FAILED",
                message=f"AI request processing failed: {str(e)}"
            )
    
    def _get_model_type(self, request_type: RequestType, model_name: str = None) -> ModelType:
        """Determine model type based on request type and model name"""
        if request_type == RequestType.IMAGE_GENERATION:
            if model_name and "flux" in model_name.lower():
                return ModelType.FLUX_1
            return ModelType.STABLE_DIFFUSION
        elif request_type == RequestType.CODE_GENERATION:
            return ModelType.CODE_LLAMA
        elif request_type == RequestType.TEXT_GENERATION:
            return ModelType.LLAMA
        else:
            return ModelType.STABLE_DIFFUSION  # Default
    
    async def _check_model_access(self, user_tier: SubscriptionTier, model_type: ModelType) -> bool:
        """Check if user tier has access to model"""
        model_tier = self.model_manager.get_model_tier(model_type)
        
        tier_hierarchy = {
            SubscriptionTier.FREE: [ModelTier.BASIC],
            SubscriptionTier.BASIC: [ModelTier.BASIC, ModelTier.STANDARD],
            SubscriptionTier.PRO: [ModelTier.BASIC, ModelTier.STANDARD, ModelTier.ADVANCED],
            SubscriptionTier.ENTERPRISE: [ModelTier.BASIC, ModelTier.STANDARD, ModelTier.ADVANCED, ModelTier.ENTERPRISE]
        }
        
        allowed_tiers = tier_hierarchy.get(user_tier, [])
        return model_tier in allowed_tiers
    
    async def _check_usage_limits(self, subscription) -> bool:
        """Check if user has remaining usage for the month"""
        if subscription.tier == SubscriptionTier.ENTERPRISE:
            return True  # Unlimited
        
        return subscription.monthly_requests_used < subscription.monthly_request_limit
    
    def _generate_cache_key(self, model_type: ModelType, prompt: str, parameters: Dict) -> str:
        """Generate cache key for request"""
        cache_data = {
            "model_type": model_type.value,
            "prompt": prompt,
            "parameters": parameters
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    async def _post_process_result(
        self, 
        result: Dict[str, Any], 
        tier: SubscriptionTier, 
        request_type: RequestType
    ) -> Dict[str, Any]:
        """Post-process AI result based on user tier"""
        processed_result = result.copy()
        
        # Add watermark for free tier
        if tier == SubscriptionTier.FREE and request_type == RequestType.IMAGE_GENERATION:
            if "image_url" in processed_result:
                processed_result["image_url"] = await self._add_watermark(processed_result["image_url"])
                processed_result["watermarked"] = True
        
        return processed_result
    
    async def _add_watermark(self, image_url: str) -> str:
        """Add watermark to generated image"""
        try:
            # This would be implemented in utils/image_processing.py
            watermarked_url = await add_watermark(image_url, "EliteForge AI - Free Tier")
            return watermarked_url
        except Exception as e:
            logger.error(f"Failed to add watermark: {str(e)}")
            return image_url  # Return original if watermarking fails
    
    # Additional helper methods would be implemented here...


class AIRequestQueue:
    """Manages AI request queuing and processing"""
    
    def __init__(self):
        self.processing_queue = asyncio.Queue()
        self.priority_queue = asyncio.Queue()
    
    async def add_to_queue(self, ai_request: AIRequest, priority: int = 0) -> Dict[str, Any]:
        """Add request to appropriate queue based on priority"""
        if priority > 0:
            await self.priority_queue.put(ai_request)
        else:
            await self.processing_queue.put(ai_request)
        
        # For now, process immediately (in production, this would be handled by workers)
        return await self._process_request(ai_request)
    
    async def _process_request(self, ai_request: AIRequest) -> Dict[str, Any]:
        """Process individual AI request"""
        # This is a placeholder - actual processing would be implemented
        # based on the specific model type and request parameters
        
        start_time = time.time()
        
        # Simulate processing time
        await asyncio.sleep(1)
        
        processing_time = time.time() - start_time
        
        return {
            "status": "completed",
            "processing_time": processing_time,
            "result": "Placeholder result - implement actual model processing"
        }


# Export main service
__all__ = ["AIService", "AIModelManager", "ModelType", "ModelTier"]
