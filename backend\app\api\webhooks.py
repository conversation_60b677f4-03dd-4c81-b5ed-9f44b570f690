"""
EliteForge AI - Payment Webhook Handlers
Handle Payoneer webhook events for payment processing and subscription management
"""

import logging
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.exceptions import CustomException
from app.services.payoneer_service import PayoneerService
from app.services.webhook_processor import WebhookProcessor

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/webhooks", tags=["webhooks"])


@router.post("/payoneer")
async def handle_payoneer_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Handle Payoneer webhook events"""
    try:
        # Get raw payload and signature
        payload = await request.body()
        signature = request.headers.get("Payoneer-Signature", "")
        
        if not signature:
            logger.warning("Payoneer webhook received without signature")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing webhook signature"
            )
        
        # Process webhook
        payoneer_service = PayoneerService(db)
        result = await payoneer_service.handle_webhook(payload, signature)
        
        logger.info(f"Payoneer webhook processed successfully: {result}")
        
        return {"status": "success", "result": result}
        
    except CustomException as e:
        logger.error(f"Payoneer webhook processing failed: {e.message}")
        raise HTTPException(
            status_code=e.status_code,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Unexpected error in Payoneer webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Webhook processing failed"
        )
    finally:
        await payoneer_service.close()


@router.post("/stripe")
async def handle_stripe_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Handle Stripe webhook events (fallback payment processor)"""
    try:
        payload = await request.body()
        signature = request.headers.get("Stripe-Signature", "")
        
        if not signature:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing webhook signature"
            )
        
        # Process Stripe webhook
        webhook_processor = WebhookProcessor(db)
        result = await webhook_processor.handle_stripe_webhook(payload, signature)
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"Stripe webhook processing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Webhook processing failed"
        )


@router.post("/internal/billing-cycle")
async def trigger_billing_cycle(
    db: AsyncSession = Depends(get_db)
):
    """Trigger billing cycle processing (internal endpoint)"""
    try:
        from app.services.subscription_service import SubscriptionManager
        
        subscription_manager = SubscriptionManager(db)
        result = await subscription_manager.process_billing_cycle()
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"Billing cycle processing failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Billing cycle processing failed"
        )


@router.post("/internal/usage-aggregation")
async def trigger_usage_aggregation(
    db: AsyncSession = Depends(get_db)
):
    """Trigger usage data aggregation (internal endpoint)"""
    try:
        webhook_processor = WebhookProcessor(db)
        result = await webhook_processor.aggregate_usage_data()
        
        return {"status": "success", "result": result}
        
    except Exception as e:
        logger.error(f"Usage aggregation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Usage aggregation failed"
        )


@router.get("/health")
async def webhook_health_check():
    """Health check endpoint for webhook service"""
    return {
        "status": "healthy",
        "service": "webhook_handler",
        "timestamp": "2024-01-01T00:00:00Z"
    }
