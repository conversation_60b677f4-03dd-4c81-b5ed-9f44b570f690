import React, { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { 
  SparklesIcon, 
  ClipboardDocumentIcon, 
  ArrowDownTrayIcon,
  AdjustmentsHorizontalIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { aiApi } from '../../lib/api'
import { formatNumber } from '../../lib/utils'

interface TextGenerationPanelProps {
  onGenerationStart: () => void
  onGenerationComplete: () => void
  onGenerationError: () => void
  isGenerating: boolean
  subscription?: any
  models: any[]
}

const textModels = [
  { id: 'llama-2-7b', name: 'Llama 2 7B', description: 'General purpose text generation' },
  { id: 'llama-2-13b', name: 'Llama 2 13B', description: 'Enhanced text generation (Advanced+)' },
  { id: 'custom-nlp', name: 'Custom NLP', description: 'Specialized text processing (Enterprise)' },
]

const presets = [
  {
    name: 'Creative Writing',
    prompt: 'Write a creative story about',
    temperature: 0.8,
    max_tokens: 1000,
  },
  {
    name: 'Technical Writing',
    prompt: 'Write a technical explanation about',
    temperature: 0.3,
    max_tokens: 800,
  },
  {
    name: 'Marketing Copy',
    prompt: 'Write compelling marketing copy for',
    temperature: 0.7,
    max_tokens: 500,
  },
  {
    name: 'Blog Article',
    prompt: 'Write a comprehensive blog article about',
    temperature: 0.6,
    max_tokens: 1500,
  },
]

export default function TextGenerationPanel({
  onGenerationStart,
  onGenerationComplete,
  onGenerationError,
  isGenerating,
  subscription,
  models
}: TextGenerationPanelProps) {
  const [prompt, setPrompt] = useState('')
  const [systemPrompt, setSystemPrompt] = useState('')
  const [selectedModel, setSelectedModel] = useState('llama-2-7b')
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [parameters, setParameters] = useState({
    max_tokens: 500,
    temperature: 0.7,
    top_p: 0.9,
  })
  const [result, setResult] = useState<any>(null)

  const generateMutation = useMutation({
    mutationFn: (data: any) => aiApi.generateText(data),
    onMutate: () => {
      onGenerationStart()
      setResult(null)
    },
    onSuccess: (response) => {
      setResult(response.data)
      onGenerationComplete()
    },
    onError: (error) => {
      console.error('Text generation failed:', error)
      onGenerationError()
    },
  })

  const handleGenerate = () => {
    if (!prompt.trim()) return

    generateMutation.mutate({
      prompt: prompt.trim(),
      model: selectedModel,
      system_prompt: systemPrompt.trim() || undefined,
      ...parameters,
    })
  }

  const handlePresetSelect = (preset: typeof presets[0]) => {
    setPrompt(preset.prompt)
    setParameters(prev => ({
      ...prev,
      temperature: preset.temperature,
      max_tokens: preset.max_tokens,
    }))
  }

  const handleCopyResult = () => {
    if (result?.result) {
      navigator.clipboard.writeText(result.result)
    }
  }

  const handleDownloadResult = () => {
    if (result?.result) {
      const blob = new Blob([result.result], { type: 'text/plain' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'generated-text.txt'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  const canUseModel = (modelId: string) => {
    if (!subscription) return false
    const tier = subscription.tier.toLowerCase()
    
    if (modelId.includes('13b') && !['advanced', 'enterprise'].includes(tier)) return false
    if (modelId.includes('custom') && tier !== 'enterprise') return false
    
    return true
  }

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <SparklesIcon className="h-6 w-6 text-blue-500 mr-3" />
        <h3 className="text-lg font-semibold text-gray-900">Text Generation</h3>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          {/* Presets */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quick Presets
            </label>
            <div className="grid grid-cols-2 gap-2">
              {presets.map((preset) => (
                <button
                  key={preset.name}
                  onClick={() => handlePresetSelect(preset)}
                  disabled={isGenerating}
                  className="p-2 text-left border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <div className="text-sm font-medium text-gray-900">{preset.name}</div>
                  <div className="text-xs text-gray-500 truncate">{preset.prompt}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Model Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Model
            </label>
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              disabled={isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
            >
              {textModels.map((model) => (
                <option 
                  key={model.id} 
                  value={model.id}
                  disabled={!canUseModel(model.id)}
                >
                  {model.name} - {model.description}
                  {!canUseModel(model.id) ? ' (Upgrade Required)' : ''}
                </option>
              ))}
            </select>
          </div>

          {/* System Prompt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              System Prompt (Optional)
            </label>
            <textarea
              value={systemPrompt}
              onChange={(e) => setSystemPrompt(e.target.value)}
              disabled={isGenerating}
              placeholder="Set the AI's behavior and context..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
              rows={2}
            />
          </div>

          {/* Main Prompt */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prompt
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              disabled={isGenerating}
              placeholder="Enter your text generation prompt..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50"
              rows={4}
            />
          </div>

          {/* Advanced Parameters */}
          <div>
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-sm text-primary-600 hover:text-primary-700"
            >
              <AdjustmentsHorizontalIcon className="h-4 w-4 mr-1" />
              Advanced Parameters
            </button>
            
            {showAdvanced && (
              <div className="mt-3 space-y-3 p-3 bg-gray-50 rounded-lg">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Tokens: {parameters.max_tokens}
                  </label>
                  <input
                    type="range"
                    min="50"
                    max="2000"
                    value={parameters.max_tokens}
                    onChange={(e) => setParameters(prev => ({ ...prev, max_tokens: parseInt(e.target.value) }))}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Temperature: {parameters.temperature}
                  </label>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={parameters.temperature}
                    onChange={(e) => setParameters(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Top P: {parameters.top_p}
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={parameters.top_p}
                    onChange={(e) => setParameters(prev => ({ ...prev, top_p: parseFloat(e.target.value) }))}
                    disabled={isGenerating}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Generate Button */}
          <button
            onClick={handleGenerate}
            disabled={!prompt.trim() || isGenerating}
            className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating...
              </>
            ) : (
              <>
                <SparklesIcon className="h-4 w-4 mr-2" />
                Generate Text
              </>
            )}
          </button>
        </div>

        {/* Result Section */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">Generated Text</h4>
            {result && (
              <div className="flex space-x-2">
                <button
                  onClick={handleCopyResult}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Copy to clipboard"
                >
                  <ClipboardDocumentIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={handleDownloadResult}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                  title="Download as file"
                >
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>

          <div className="border border-gray-200 rounded-lg p-4 min-h-[300px] bg-gray-50">
            {isGenerating ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
                  <p className="text-gray-500">Generating your text...</p>
                </div>
              </div>
            ) : result ? (
              <div className="space-y-4">
                <div className="prose max-w-none">
                  <pre className="whitespace-pre-wrap text-sm text-gray-900 font-sans">
                    {result.result}
                  </pre>
                </div>
                
                {/* Generation Info */}
                <div className="pt-4 border-t border-gray-200 text-xs text-gray-500 space-y-1">
                  <div>Processing time: {result.processing_time?.toFixed(2)}s</div>
                  <div>Model: {selectedModel}</div>
                  {result.cached && <div>Result was cached</div>}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400">
                <div className="text-center">
                  <SparklesIcon className="h-12 w-12 mx-auto mb-2" />
                  <p>Generated text will appear here</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
